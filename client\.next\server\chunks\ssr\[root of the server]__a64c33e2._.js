module.exports = {

"[project]/src/lib/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearStudentAuthToken": (()=>clearStudentAuthToken),
    "cn": (()=>cn),
    "getStudentAuthToken": (()=>getStudentAuthToken),
    "isAuthenticated": (()=>isAuthenticated),
    "isStudentAuthenticated": (()=>isStudentAuthenticated),
    "setStudentAuthToken": (()=>setStudentAuthToken),
    "truncateThought": (()=>truncateThought)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
const truncateThought = (text, wordLimit = 5)=>{
    const words = text.trim().split(/\s+/);
    if (words.length <= wordLimit) return text;
    return words.slice(0, wordLimit).join(' ') + '...';
};
const setStudentAuthToken = (token)=>{
    localStorage.setItem('studentToken', token);
};
const getStudentAuthToken = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return null;
};
const clearStudentAuthToken = ()=>{
    localStorage.removeItem('studentToken');
};
const isStudentAuthenticated = ()=>{
    return !!getStudentAuthToken();
};
const isAuthenticated = ()=>{
    const studentToken = getStudentAuthToken();
    if (studentToken) {
        return {
            isAuth: true,
            userType: 'STUDENT'
        };
    }
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return {
        isAuth: false,
        userType: null
    };
};
}}),
"[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>Button),
    "buttonVariants": (()=>buttonVariants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
;
const buttonVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", {
    variants: {
        variant: {
            default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
            destructive: 'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
            outline: 'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
            secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
            ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',
            link: 'text-primary underline-offset-4 hover:underline'
        },
        size: {
            default: 'h-9 px-4 py-2 has-[>svg]:px-3',
            sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
            lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',
            icon: 'size-9'
        }
    },
    defaultVariants: {
        variant: 'default',
        size: 'default'
    }
});
function Button({ className, variant, size, asChild = false, ...props }) {
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Slot"] : 'button';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        "data-slot": "button",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(buttonVariants({
            variant,
            size,
            className
        })),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/button.tsx",
        lineNumber: 48,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/src/services/studentDetailServiceApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getStudentDetail": (()=>getStudentDetail)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-ssr] (ecmascript)");
;
const getStudentDetail = async (studentId)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].get(`/uwhizStudentData/${studentId}`);
        return response.data;
    } catch (error) {
        return {
            success: false,
            error: `Failed To Get Student Detail: ${error.response?.data?.message || error.message}`
        };
    }
};
}}),
"[project]/src/services/mock-exam-resultApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getMockExamResults": (()=>getMockExamResults),
    "saveMockExamResult": (()=>saveMockExamResult)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-ssr] (ecmascript)");
;
const saveMockExamResult = async (data)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].post("/mock-exam-result", data, {
            headers: {
                "Server-Select": "uwhizServer"
            }
        });
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        return {
            success: false,
            error: `Failed to save mock exam result: ${error.response?.data?.message || error.message}`
        };
    }
};
const getMockExamResults = async (studentId, page = 1, limit = 10, filter = {})=>{
    try {
        const query = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
            ...filter.isWeekly !== undefined && {
                isWeekly: filter.isWeekly.toString()
            }
        }).toString();
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].get(`/mock-exam-result/${studentId}?${query}`, {
            headers: {
                "Server-Select": "uwhizServer"
            }
        });
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        return {
            success: false,
            error: `Failed to get mock exam result: ${error.response?.data?.message || error.message}`
        };
    }
};
}}),
"[project]/src/services/uwhizMockExamTerminationApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "countUwhizAttemp": (()=>countUwhizAttemp),
    "saveTerminatedStudent": (()=>saveTerminatedStudent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-ssr] (ecmascript)");
;
const saveTerminatedStudent = async (data)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].post('/mock-exam-terminate', data, {
            headers: {
                "Server-Select": "uwhizServer"
            }
        });
        return response.data;
    } catch (error) {
        return {
            success: false,
            error: `Failed to save termination log: ${error.response?.data?.message || error.message}`
        };
    }
};
const countUwhizAttemp = async (studentId)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].get(`mock-exam-terminate/count?studentId=${studentId}`, {
            headers: {
                "Server-Select": "uwhizServer"
            }
        });
        return response.data;
    } catch (error) {
        return {
            success: false,
            error: `Failed To Get Count of termination: ${error.response?.data?.message || error.message}`
        };
    }
};
}}),
"[project]/public/uwhizExam.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/uwhizExam.a0751471.png");}}),
"[project]/public/uwhizExam.png.mjs { IMAGE => \"[project]/public/uwhizExam.png (static in ecmascript)\" } [app-ssr] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$uwhizExam$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/uwhizExam.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$uwhizExam$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 798,
    height: 626,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAYAAAD+Bd/7AAAAjUlEQVR42lXMyw6CMBCF4UYot1qgBROwCiQiYY/s6vs/1i+wMLqZycz5coQQAhWH3EzG3WYMlTr2/tszsY88kRSppC1SXJlSqfi4v0AGJ6LwRLOBi46xKvoHQRDgnOPRXQ9Un38akiTBGMM0Pen7jrY2NDantiVSbmgYetb1hfce//bM88w4jizLgtaaD9PlOXjFshcbAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 6
};
}}),
"[project]/src/services/uwhizMockExamApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "uwhizMockQuestionForStudent": (()=>uwhizMockQuestionForStudent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-ssr] (ecmascript)");
;
const uwhizMockQuestionForStudent = async (studentId, medium, isWeekly)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].get(`mock-exam/${studentId}/${medium}`, {
            params: {
                isWeekly: isWeekly.toString()
            },
            headers: {
                'Server-Select': 'uwhizServer'
            }
        });
        return response.data;
    } catch (error) {
        throw new Error(`Failed To Get Question: ${error.response?.data?.message || error.message}`);
    }
};
}}),
"[project]/src/app/mock-test/restictExamAttempt.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isAttemptAllowed": (()=>isAttemptAllowed),
    "saveExamAttempt": (()=>saveExamAttempt)
});
const saveExamAttempt = (studentId)=>{
    const timestamp = Date.now();
    const examAttempts = localStorage.getItem("examAttempts");
    const attempts = examAttempts ? JSON.parse(examAttempts) : {};
    attempts[studentId] = timestamp;
    localStorage.setItem("examAttempts", JSON.stringify(attempts));
};
const isAttemptAllowed = (studentId)=>{
    const examAttempts = localStorage.getItem("examAttempts");
    if (!examAttempts) {
        return {
            allowed: true,
            remainingHours: null
        };
    }
    const attempts = JSON.parse(examAttempts);
    const lastAttempt = attempts[studentId];
    if (!lastAttempt) {
        return {
            allowed: true,
            remainingHours: null
        };
    }
    const lastAttemptTime = lastAttempt;
    const currentTime = Date.now();
    const hours24InMs = 24 * 60 * 60 * 1000;
    const timeSinceLastAttempt = currentTime - lastAttemptTime;
    if (timeSinceLastAttempt >= hours24InMs) {
        return {
            allowed: true,
            remainingHours: null
        };
    }
    const remainingMs = hours24InMs - timeSinceLastAttempt;
    const remainingHours = Math.ceil(remainingMs / (60 * 60 * 1000));
    return {
        allowed: false,
        remainingHours
    };
};
}}),
"[project]/src/services/uestCoinTransctionApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addUestCoinTranscation": (()=>addUestCoinTranscation),
    "updateUestCoins": (()=>updateUestCoins)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-ssr] (ecmascript)");
;
const addUestCoinTranscation = async (data)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].post("/uwhizCoinTransction/add", data);
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        return {
            success: false,
            error: `Failed to log transction of coins in mock exam: ${error.response?.data?.message || error.message}`
        };
    }
};
const updateUestCoins = async (data)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].post("/uwhizCoinTransction/update", data);
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        return {
            success: false,
            error: `Failed to update coins: ${error.response?.data?.message || error.message}`
        };
    }
};
}}),
"[project]/src/services/mockExamStreakApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getMockExamStreak": (()=>getMockExamStreak),
    "getweeklyMockExamStreak": (()=>getweeklyMockExamStreak),
    "saveMockExamStreak": (()=>saveMockExamStreak),
    "saveweeklyMockExamStreak": (()=>saveweeklyMockExamStreak)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-ssr] (ecmascript)");
;
const saveMockExamStreak = async (studentId)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].put(`/mock-exam-streak/${studentId}`, {}, {
            headers: {
                "Server-Select": "uwhizServer"
            }
        });
        return {
            success: true,
            data: response.data.data
        };
    } catch (error) {
        return {
            success: false,
            error: `Failed to save mock exam streak: ${error.response?.data?.error || error.message}`
        };
    }
};
const getMockExamStreak = async (studentId)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].get(`/mock-exam-streak/${studentId}`, {
            headers: {
                "Server-Select": "uwhizServer"
            }
        });
        return {
            success: true,
            data: response.data.data
        };
    } catch (error) {
        return {
            success: false,
            error: `Failed to get mock exam streak: ${error.response?.data?.error || error.message}`
        };
    }
};
const saveweeklyMockExamStreak = async (studentId)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].put(`/mock-exam-weekly-streak/${studentId}`, {}, {
            headers: {
                "Server-Select": "uwhizServer"
            }
        });
        return {
            success: true,
            data: response.data.data
        };
    } catch (error) {
        return {
            success: false,
            error: `Failed to save mock exam streak: ${error.response?.data?.error || error.message}`
        };
    }
};
const getweeklyMockExamStreak = async (studentId)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].get(`/mock-exam-weekly-streak/${studentId}`, {
            headers: {
                "Server-Select": "uwhizServer"
            }
        });
        return {
            success: true,
            data: response.data.data
        };
    } catch (error) {
        return {
            success: false,
            error: `Failed to get mock exam streak: ${error.response?.data?.error || error.message}`
        };
    }
};
}}),
"[project]/src/hooks/getStudentId.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const useStudentId = ()=>{
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const [studentId, setStudentId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const init = async ()=>{
            const token = searchParams.get('token');
            if (token) {
                try {
                    const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosInstance"].get(`/student/login-with-jwt`, {
                        params: {
                            token
                        },
                        withCredentials: true
                    });
                    const data = response.data.data;
                    if (data?.userId) {
                        const studentData = {
                            id: data.userId,
                            contactNo: data.contactNo,
                            firstName: data.firstName,
                            lastName: data.lastName
                        };
                        localStorage.setItem('student_data', JSON.stringify(studentData));
                        localStorage.setItem('studentToken', token);
                        localStorage.setItem('mobile_request', "true");
                        setStudentId(data.userId);
                    }
                    const newUrl = window.location.pathname;
                    router.replace(newUrl);
                } catch (error) {
                    console.error('JWT login failed:', error);
                }
            } else {
                const local = localStorage.getItem('student_data');
                const parsed = local ? JSON.parse(local) : null;
                setStudentId(parsed?.id || null);
            }
        };
        init();
    }, [
        searchParams,
        router
    ]);
    return studentId;
};
const __TURBOPACK__default__export__ = useStudentId;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[project]/src/app/mock-test/mockExam.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>QuizPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$studentDetailServiceApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/studentDetailServiceApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$mock$2d$exam$2d$resultApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/mock-exam-resultApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uwhizMockExamTerminationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/uwhizMockExamTerminationApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader.js [app-ssr] (ecmascript) <export default as Loader>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$uwhizExam$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$uwhizExam$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/uwhizExam.png.mjs { IMAGE => "[project]/public/uwhizExam.png (static in ecmascript)" } [app-ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uwhizMockExamApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/uwhizMockExamApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$mock$2d$test$2f$restictExamAttempt$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/mock-test/restictExamAttempt.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uestCoinTransctionApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/uestCoinTransctionApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$mockExamStreakApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/mockExamStreakApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$getStudentId$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/getStudentId.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$share$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-share/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html$2d$to$2d$image$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html-to-image/es/index.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const QuizHeader = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].memo(()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: "fixed top-0 left-0 right-0 z-20 py-2 px-4 sm:px-6 sm:py-3 flex flex-col sm:flex-row items-center justify-between bg-black text-white shadow-md",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center gap-3",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    height: 60,
                    width: 60,
                    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$uwhizExam$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$uwhizExam$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"].src,
                    alt: "Uwhiz Logo",
                    quality: 100,
                    className: "object-contain sm:h-20 sm:w-20"
                }, void 0, false, {
                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                    lineNumber: 47,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    className: "text-lg sm:text-2xl font-bold tracking-tight",
                    children: "Uest Daily Quiz"
                }, void 0, false, {
                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                    lineNumber: 55,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/mock-test/mockExam.tsx",
            lineNumber: 46,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/mock-test/mockExam.tsx",
        lineNumber: 45,
        columnNumber: 7
    }, this);
});
QuizHeader.displayName = "QuizHeader";
function QuizPage() {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const studentId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$getStudentId$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const [isWeekly, setIsWeekly] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoginDialogOpen, setIsLoginDialogOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isProfileDialogOpen, setIsProfileDialogOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isDialogOpen, setIsDialogOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isExamTakenDialogOpen, setIsExamTakenDialogOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showWarning, setShowWarning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showTermination, setShowTermination] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [questions, setQuestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [currentQuestionIndex, setCurrentQuestionIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [timeLeft, setTimeLeft] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [userAnswers, setUserAnswers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isQuizCompleted, setIsQuizCompleted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedAnswer, setSelectedAnswer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isApiCallPending, setIsApiCallPending] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const tickSoundRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [terminationMessage, setTerminationMessage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [violationCount, setViolationCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [showAnswerFeedback, setShowAnswerFeedback] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [streakData, setStreakData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [streakLoading, setStreakLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [streakError, setStreakError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const cardRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isProfileNotApproved, setIsProfileNotApproved] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const isWeeklyParam = searchParams.get("isWeekly");
        setIsWeekly(isWeeklyParam === "true");
    }, [
        searchParams
    ]);
    const initializeViolationCounts = async ()=>{
        if (!studentId) {
            return 0;
        }
        try {
            const count = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uwhizMockExamTerminationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["countUwhizAttemp"])(studentId);
            return typeof count === 'number' ? count : 0;
        } catch (error) {
            console.error("Failed to fetch violation count:", error);
            return 0;
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const fetchCounts = async ()=>{
            const count = await initializeViolationCounts();
            setViolationCount(count);
        };
        fetchCounts();
    }, [
        studentId
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (violationCount >= 3) {
            setShowTermination(true);
            setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
            if (studentId) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$mock$2d$test$2f$restictExamAttempt$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveExamAttempt"])(studentId);
            }
        }
    }, [
        violationCount,
        studentId
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (studentId) {
            const checkExamEligibility = async ()=>{
                try {
                    const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$mock$2d$exam$2d$resultApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getMockExamResults"])(studentId, 1, 1, {
                        isWeekly
                    });
                    if (response.success && response.data.data.mockExamResults.length > 0) {
                        const latestExam = response.data.data.mockExamResults[0];
                        const examDate = new Date(latestExam.createdAt).toISOString().split('T')[0];
                        const today = new Date().toISOString().split('T')[0];
                        if (examDate === today && !isWeekly) {
                            setIsExamTakenDialogOpen(true);
                        } else if (isWeekly) {
                            const startOfWeek = new Date();
                            startOfWeek.setDate(startOfWeek.getDate() - (startOfWeek.getDay() === 0 ? 6 : startOfWeek.getDay() - 1));
                            startOfWeek.setHours(0, 0, 0, 0);
                            const hasAttemptedThisWeek = new Date(latestExam.createdAt) >= startOfWeek;
                            if (hasAttemptedThisWeek) {
                                setIsExamTakenDialogOpen(true);
                            }
                        }
                    }
                } catch (error) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to verify exam eligibility.", error);
                }
            };
            checkExamEligibility();
        }
    }, [
        studentId,
        isWeekly
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        tickSoundRef.current = new Audio("/clock-ticking-sound-effect.mp3");
        tickSoundRef.current.loop = true;
        return ()=>{
            if (tickSoundRef.current) {
                tickSoundRef.current.pause();
                tickSoundRef.current = null;
            }
        };
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (questions.length > 0 && timeLeft <= 5 && timeLeft > 0 && !isDialogOpen && !showTermination && !isQuizCompleted && !isLoginDialogOpen && !isProfileDialogOpen && !isExamTakenDialogOpen && tickSoundRef.current) {
            tickSoundRef.current.play().catch((error)=>{
                console.error("Failed to play tick sound:", error);
            });
        } else if (tickSoundRef.current) {
            tickSoundRef.current.pause();
        }
    }, [
        timeLeft,
        questions,
        isDialogOpen,
        showTermination,
        isQuizCompleted,
        isLoginDialogOpen,
        isProfileDialogOpen,
        isExamTakenDialogOpen
    ]);
    const handleNextQuestion = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        const currentQuestion = questions[currentQuestionIndex];
        if (selectedAnswer) {
            const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
            setUserAnswers((prev)=>[
                    ...prev,
                    {
                        questionId: currentQuestion.id,
                        selectedAnswer,
                        isCorrect
                    }
                ]);
            setShowAnswerFeedback(true);
            setTimeout(()=>{
                setShowAnswerFeedback(false);
                setSelectedAnswer(null);
                if (currentQuestionIndex < questions.length - 1) {
                    setCurrentQuestionIndex((prev)=>prev + 1);
                } else {
                    setIsQuizCompleted(true);
                }
            }, 1000);
        } else {
            setUserAnswers((prev)=>[
                    ...prev,
                    {
                        questionId: currentQuestion.id,
                        selectedAnswer: "skipped",
                        isCorrect: false
                    }
                ]);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].warning("Question skipped.");
            if (currentQuestionIndex < questions.length - 1) {
                setCurrentQuestionIndex((prev)=>prev + 1);
            } else {
                setIsQuizCompleted(true);
            }
        }
    }, [
        selectedAnswer,
        questions,
        currentQuestionIndex
    ]);
    const calculateScore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return userAnswers.reduce((score, answer)=>score + (answer.isCorrect ? 1 : 0), 0);
    }, [
        userAnswers
    ]);
    const calculateCoins = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        const percentage = calculateScore / questions.length * 100;
        let baseCoins;
        if (percentage >= 100) baseCoins = 5;
        else if (percentage >= 90) baseCoins = 4;
        else if (percentage >= 80) baseCoins = 3;
        else if (percentage >= 70) baseCoins = 2;
        else if (percentage >= 60) baseCoins = 1;
        else baseCoins = 0;
        return isWeekly ? baseCoins * 5 : baseCoins;
    }, [
        calculateScore,
        questions.length,
        isWeekly
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isQuizCompleted && studentId) {
            const saveResultAndCoins = async ()=>{
                try {
                    const streakResponse = isWeekly ? await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$mockExamStreakApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveweeklyMockExamStreak"])(studentId) : await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$mockExamStreakApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveMockExamStreak"])(studentId);
                    if (!streakResponse.success) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(streakResponse.error);
                        setStreakError(streakResponse.error);
                        return;
                    }
                    setStreakLoading(true);
                    const streakFetchResponse = isWeekly ? await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$mockExamStreakApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getweeklyMockExamStreak"])(studentId) : await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$mockExamStreakApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getMockExamStreak"])(studentId);
                    setStreakLoading(false);
                    if (streakFetchResponse.success) {
                        setStreakData(streakFetchResponse.data);
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(`Streak updated successfully! Current streak: ${streakFetchResponse.data.streak} ${isWeekly ? 'Weeks' : 'Days'}`);
                    } else {
                        setStreakError(streakFetchResponse.error);
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(streakFetchResponse.error);
                    }
                    const streakCoins = 1;
                    const totalCoins = calculateCoins + streakCoins;
                    const resultData = {
                        studentId,
                        score: calculateScore,
                        coinEarnings: totalCoins,
                        isWeekly
                    };
                    const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$mock$2d$exam$2d$resultApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveMockExamResult"])(resultData);
                    if (response.success) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Result saved successfully!");
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(response.error);
                    }
                    const coinsData = {
                        modelId: studentId,
                        modelType: "STUDENT",
                        coins: totalCoins
                    };
                    const updateCoinsResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uestCoinTransctionApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateUestCoins"])(coinsData);
                    if (updateCoinsResponse.success) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(`Coins updated successfully! Added ${totalCoins} coins (Score: ${calculateCoins}, Streak: ${streakCoins}).`);
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(updateCoinsResponse.error);
                    }
                    const transactionData = {
                        modelId: studentId,
                        modelType: "STUDENT",
                        amount: totalCoins,
                        type: "CREDIT",
                        reason: `${isWeekly ? 'Weekly' : 'Daily'} Quiz Exam (Score + Streak: ${streakCoins})`
                    };
                    const addTransactionResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uestCoinTransctionApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addUestCoinTranscation"])(transactionData);
                    if (addTransactionResponse.success) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Transaction logged successfully!");
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(addTransactionResponse.error);
                    }
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$mock$2d$test$2f$restictExamAttempt$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveExamAttempt"])(studentId);
                    await exitFullScreen();
                } catch (error) {
                    setStreakLoading(false);
                    setStreakError(`Failed to fetch streak: ${error.message}`);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(`Failed to save result, update coins, or update streak: ${error.message}`);
                }
            };
            saveResultAndCoins();
        }
    }, [
        isQuizCompleted,
        studentId,
        calculateScore,
        calculateCoins,
        isWeekly
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (questions.length > 0 && timeLeft > 0 && !isDialogOpen && !showTermination && !isLoginDialogOpen && !isProfileDialogOpen && !isExamTakenDialogOpen) {
            const timer = setInterval(()=>{
                setTimeLeft((prev)=>{
                    const newTime = prev - 1;
                    if (newTime <= 0) {
                        clearInterval(timer);
                        handleNextQuestion();
                        return 0;
                    }
                    return newTime;
                });
            }, 1000);
            return ()=>clearInterval(timer);
        }
    }, [
        timeLeft,
        questions,
        currentQuestionIndex,
        isDialogOpen,
        showTermination,
        isLoginDialogOpen,
        isProfileDialogOpen,
        isExamTakenDialogOpen,
        handleNextQuestion
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (questions.length > 0 && !isDialogOpen && !showTermination && !isLoginDialogOpen && !isProfileDialogOpen && !isExamTakenDialogOpen) {
            const newTime = 45;
            setTimeLeft(newTime);
        }
    }, [
        currentQuestionIndex,
        questions,
        isDialogOpen,
        showTermination,
        isLoginDialogOpen,
        isProfileDialogOpen,
        isExamTakenDialogOpen
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }, [
        isQuizCompleted
    ]);
    const fetchQuizState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (!studentId) {
            setIsLoginDialogOpen(true);
            return;
        }
        try {
            const studentResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$studentDetailServiceApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getStudentDetail"])(studentId);
            if (!studentResponse.success) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(studentResponse.error);
                setIsProfileDialogOpen(true);
                return;
            }
            if (studentResponse.data.status != "APPROVED") {
                setIsProfileDialogOpen(true);
                setIsProfileNotApproved(true);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Student Profile is not approved yet. Please check your profile.");
                return;
            }
            const medium = studentResponse.data.medium.toUpperCase();
            const questionResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uwhizMockExamApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["uwhizMockQuestionForStudent"])(studentId, medium, isWeekly);
            if (questionResponse && Array.isArray(questionResponse)) {
                setQuestions(questionResponse);
                setTimeLeft(45);
                setIsDialogOpen(true);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("No questions found or invalid response.");
            }
        } catch (error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(error);
        }
    }, [
        studentId,
        isWeekly
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (studentId && !isExamTakenDialogOpen) {
            fetchQuizState();
        }
    }, [
        studentId,
        fetchQuizState,
        isExamTakenDialogOpen
    ]);
    const exitFullScreen = async (maxAttempts = 3, attempt = 1)=>{
        try {
            if (document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement) {
                if (document.exitFullscreen) {
                    await document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    await document.webkitExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    await document.mozCancelFullScreen();
                }
                await new Promise((resolve)=>setTimeout(resolve, 100));
                if (!document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement) {
                    return true;
                }
                if (attempt < maxAttempts) {
                    return await exitFullScreen(maxAttempts, attempt + 1);
                }
                throw new Error("Max attempts reached");
            }
            return true;
        } catch (err) {
            console.error(`Failed to exit full-screen mode (attempt ${attempt}):`, err);
            if (attempt < maxAttempts) {
                await new Promise((resolve)=>setTimeout(resolve, 500));
                return await exitFullScreen(maxAttempts, attempt + 1);
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to exit full-screen mode. Please press Esc to exit manually.");
            return false;
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isQuizCompleted && studentId && showTermination) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$mock$2d$test$2f$restictExamAttempt$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveExamAttempt"])(studentId);
            exitFullScreen();
        }
    }, [
        isQuizCompleted,
        studentId
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (showTermination && studentId) {
            exitFullScreen().then((success)=>{
                console.log("Quiz terminated and full-screen exited.", success);
            });
        }
    }, [
        showTermination,
        studentId
    ]);
    const enterFullScreen = ()=>{
        const element = document.documentElement;
        if (element.requestFullscreen) {
            element.requestFullscreen().catch((err)=>console.error("Failed to enter fullscreen:", err));
        }
    };
    const handleStartQuiz = ()=>{
        setIsDialogOpen(false);
        enterFullScreen();
        if (questions.length > 0) {
            setTimeLeft(45);
        }
    };
    const handleGoToProfile = ()=>{
        router.push("/student/profile");
    };
    const handleCloseExamTakenDialog = ()=>{
        setIsExamTakenDialogOpen(false);
        router.push("/mock-exam-card");
    };
    const handleKeyDown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (event)=>{
        if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;
        const restrictedKeys = [
            "Alt",
            "Control",
            "Tab",
            "Shift",
            "Enter"
        ];
        const functionKeys = [
            "F1",
            "F2",
            "F3",
            "F4",
            "F5",
            "F6",
            "F7",
            "F8",
            "F9",
            "F10",
            "F11",
            "F12"
        ];
        const isDevToolsShortcut = event.ctrlKey && event.shiftKey && (event.key === "I" || event.key === "J" || event.key === "C") || event.metaKey && event.altKey && event.key === "I" || event.key === "F12";
        const isCopyShortcut = (event.ctrlKey || event.metaKey) && (event.key === "c" || event.key === "C");
        if (restrictedKeys.includes(event.key) || functionKeys.includes(event.key) || isDevToolsShortcut || isCopyShortcut) {
            event.preventDefault();
            if (isCopyShortcut) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].warning("Copying is disabled during the quiz.");
                return;
            }
            if (!studentId) {
                setViolationCount(0);
                return;
            }
            setIsApiCallPending(true);
            try {
                const violationType = isDevToolsShortcut ? "DevTools shortcut" : functionKeys.includes(event.key) ? `Function key "${event.key}"` : `Restricted key "${event.key}"`;
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uwhizMockExamTerminationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveTerminatedStudent"])({
                    studentId,
                    reason: violationType
                });
                const updatedCount = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uwhizMockExamTerminationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["countUwhizAttemp"])(studentId);
                setViolationCount(updatedCount);
                if (updatedCount === 1) {
                    setShowWarning(true);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].warning(`${violationType} detected.`);
                } else if (updatedCount === 2) {
                    setShowWarning(true);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].warning(`${violationType} detected. One more violation will terminate the quiz.`);
                } else if (updatedCount >= 3) {
                    setShowTermination(true);
                    setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
                    if (studentId) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$mock$2d$test$2f$restictExamAttempt$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveExamAttempt"])(studentId);
                    }
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Quiz terminated due to multiple cheating attempts.");
                }
            } catch (error) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to save termination record.", {
                    description: error instanceof Error ? error.message : "Unknown error"
                });
            } finally{
                setIsApiCallPending(false);
            }
        }
    }, [
        studentId,
        isDialogOpen,
        isLoginDialogOpen,
        isProfileDialogOpen,
        showWarning,
        isApiCallPending,
        isExamTakenDialogOpen
    ]);
    const handleVisibilityChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;
        if (document.hidden) {
            setIsApiCallPending(true);
            try {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uwhizMockExamTerminationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveTerminatedStudent"])({
                    studentId,
                    reason: "Tab switch"
                });
                if (!studentId) {
                    setViolationCount(0);
                    return;
                }
                const updatedCount = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uwhizMockExamTerminationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["countUwhizAttemp"])(studentId);
                setViolationCount(updatedCount);
                if (updatedCount === 1) {
                    setShowWarning(true);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].warning("Tab switch detected.");
                } else if (updatedCount === 2) {
                    setShowWarning(true);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].warning("Again tab switch detected. One more violation will terminate the quiz.");
                } else if (updatedCount >= 3) {
                    setShowTermination(true);
                    setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
                    if (studentId) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$mock$2d$test$2f$restictExamAttempt$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveExamAttempt"])(studentId);
                    }
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Quiz terminated due to multiple cheating attempts.");
                }
            } catch (error) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to save termination record.", {
                    description: error instanceof Error ? error.message : "Unknown error"
                });
            } finally{
                setIsApiCallPending(false);
            }
        }
    }, [
        studentId,
        isDialogOpen,
        isLoginDialogOpen,
        isProfileDialogOpen,
        showWarning,
        isApiCallPending,
        isExamTakenDialogOpen
    ]);
    const handleContextMenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (event)=>{
        if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;
        event.preventDefault();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].warning("Right-click is disabled during the quiz.");
    }, [
        studentId,
        isDialogOpen,
        isLoginDialogOpen,
        isProfileDialogOpen,
        showWarning,
        isApiCallPending,
        isExamTakenDialogOpen
    ]);
    const handleWindowBlur = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;
        setIsApiCallPending(true);
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uwhizMockExamTerminationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveTerminatedStudent"])({
                studentId,
                reason: "Window blur"
            });
            const updatedCount = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uwhizMockExamTerminationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["countUwhizAttemp"])(studentId);
            setViolationCount(updatedCount);
            if (updatedCount === 1) {
                setShowWarning(true);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].warning("Window focus lost.");
            } else if (updatedCount == 2) {
                setShowWarning(true);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].warning("Window focus lost again. One more violation will terminate the quiz.");
            } else if (updatedCount >= 3) {
                setShowTermination(true);
                setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
                if (studentId) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$mock$2d$test$2f$restictExamAttempt$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveExamAttempt"])(studentId);
                }
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Quiz terminated due to multiple cheating attempts.");
            }
        } catch (error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to save termination record.", {
                description: error instanceof Error ? error.message : "Unknown error"
            });
        } finally{
            setIsApiCallPending(false);
        }
    }, [
        studentId,
        isDialogOpen,
        isLoginDialogOpen,
        isProfileDialogOpen,
        showWarning,
        isApiCallPending,
        isExamTakenDialogOpen
    ]);
    const handleFullScreenChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;
        if (!document.fullscreenElement) {
            setIsApiCallPending(true);
            try {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uwhizMockExamTerminationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveTerminatedStudent"])({
                    studentId,
                    reason: "Full-screen exit"
                });
                if (!studentId) {
                    setViolationCount(0);
                    return;
                }
                const updatedCount = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$uwhizMockExamTerminationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["countUwhizAttemp"])(studentId);
                setViolationCount(updatedCount);
                if (updatedCount === 1) {
                    setShowWarning(true);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].warning("You have exited full-screen mode.");
                } else if (updatedCount === 2) {
                    setShowWarning(true);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].warning("Again you have exited full-screen mode. One more violation will terminate the quiz.");
                } else if (updatedCount >= 3) {
                    setShowTermination(true);
                    setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
                    if (studentId) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$mock$2d$test$2f$restictExamAttempt$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveExamAttempt"])(studentId);
                    }
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Quiz terminated due to multiple cheating attempts.");
                }
            } catch (error) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to save termination record.", {
                    description: error instanceof Error ? error.message : "Unknown error"
                });
            } finally{
                setIsApiCallPending(false);
            }
        }
    }, [
        studentId,
        isDialogOpen,
        isLoginDialogOpen,
        isProfileDialogOpen,
        showWarning,
        isApiCallPending,
        isExamTakenDialogOpen
    ]);
    const handleGoHome = async ()=>{
        setShowTermination(false);
        const mobileRequest = localStorage.getItem('mobile_request');
        if (mobileRequest === 'true') {
            localStorage.removeItem('mobile_request');
            window.location.href = 'UEST://DailyQuiz';
        }
        const isFullScreen = document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement;
        if (isFullScreen) {
            const success = await exitFullScreen();
            if (success) {
                router.push("/mock-exam-card");
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].warning("Could not exit full-screen mode automatically. Please press Esc to exit manually.");
                router.push("/mock-exam-card");
            }
        } else {
            router.push("/mock-exam-card");
        }
        setTimeout(()=>{
            router.push("/mock-exam-card");
        }, 1000);
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isDialogOpen && !isLoginDialogOpen && !isProfileDialogOpen && !showTermination && !isExamTakenDialogOpen) {
            document.addEventListener("visibilitychange", handleVisibilityChange);
            document.addEventListener("keydown", handleKeyDown);
            window.addEventListener("blur", handleWindowBlur);
            document.addEventListener("contextmenu", handleContextMenu);
            document.addEventListener("fullscreenchange", handleFullScreenChange);
        }
        return ()=>{
            document.removeEventListener("visibilitychange", handleVisibilityChange);
            document.removeEventListener("keydown", handleKeyDown);
            window.removeEventListener("blur", handleWindowBlur);
            document.removeEventListener("contextmenu", handleContextMenu);
            document.removeEventListener("fullscreenchange", handleFullScreenChange);
        };
    }, [
        handleVisibilityChange,
        handleKeyDown,
        handleWindowBlur,
        handleContextMenu,
        handleFullScreenChange,
        isDialogOpen,
        isLoginDialogOpen,
        isProfileDialogOpen,
        showTermination,
        isExamTakenDialogOpen
    ]);
    const formatTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((seconds)=>{
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    }, []);
    const progress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return questions.length > 0 ? (currentQuestionIndex + 1) / questions.length * 100 : 0;
    }, [
        currentQuestionIndex,
        questions
    ]);
    const getButtonClass = (optionKey)=>{
        const baseClass = `w-full h-auto min-h-[60px] sm:min-h-[80px] whitespace-normal text-wrap font-medium rounded-lg py-3 sm:py-4 text-sm sm:text-lg text-gray-700 hover:bg-orange-100 hover:border-orange-500 transition-all duration-200 flex items-start justify-start gap-3 px-3 sm:px-6 shadow-sm border border-gray-200 bg-white`;
        if (selectedAnswer === optionKey) {
            return `${baseClass} bg-orange-100 border-orange-500`;
        }
        return baseClass;
    };
    const handleOptionClick = (optionKey)=>{
        if (!showAnswerFeedback) {
            setSelectedAnswer(optionKey);
        }
    };
    if (isLoginDialogOpen) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-lg sm:text-2xl font-bold mb-4",
                        children: "Login Required"
                    }, void 0, false, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 751,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mb-4 text-sm sm:text-base text-gray-600",
                        children: "Please log in as a student to access the quiz."
                    }, void 0, false, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 752,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        onClick: ()=>router.push(`/student/login?redirect=/mock-test${isWeekly ? '?isWeekly=true' : ''}`),
                        className: "bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",
                        children: "Login to Continue"
                    }, void 0, false, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 755,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                lineNumber: 750,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/mock-test/mockExam.tsx",
            lineNumber: 749,
            columnNumber: 7
        }, this);
    }
    if (isProfileDialogOpen) {
        const heading = isProfileNotApproved ? 'Profile Not Approved' : 'Complete Your Profile';
        const message = isProfileNotApproved ? 'Your profile has not been approved yet. Please wait for approval and check notifications.' : 'Your profile is incomplete. Please complete your profile to proceed.';
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-lg sm:text-2xl font-bold mb-4",
                        children: heading
                    }, void 0, false, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 778,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mb-4 text-sm sm:text-base text-gray-600",
                        children: message
                    }, void 0, false, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 779,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        onClick: handleGoToProfile,
                        className: "bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",
                        children: isProfileNotApproved ? 'Update Profile' : 'Complete Profile'
                    }, void 0, false, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 780,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                lineNumber: 777,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/mock-test/mockExam.tsx",
            lineNumber: 776,
            columnNumber: 7
        }, this);
    }
    if (isExamTakenDialogOpen) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-lg sm:text-2xl font-bold mb-4 text-black",
                        children: "Exam Already Taken"
                    }, void 0, false, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 795,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mb-4 text-sm sm:text-base text-gray-600",
                        children: isWeekly ? "You have already attempted the weekly exam this week. Please try again next Sunday." : "You have already attempted the daily exam today. Please try again tomorrow."
                    }, void 0, false, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 796,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        onClick: handleCloseExamTakenDialog,
                        className: "bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",
                        children: "Go to Home"
                    }, void 0, false, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 801,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                lineNumber: 794,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/mock-test/mockExam.tsx",
            lineNumber: 793,
            columnNumber: 7
        }, this);
    }
    if (questions.length === 0) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-base sm:text-xl font-medium mr-4",
                    children: "Loading questions..."
                }, void 0, false, {
                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                    lineNumber: 815,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader$3e$__["Loader"], {
                    className: "w-5 h-5 sm:w-8 sm:h-8 animate-spin text-customOrange"
                }, void 0, false, {
                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                    lineNumber: 816,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/mock-test/mockExam.tsx",
            lineNumber: 814,
            columnNumber: 7
        }, this);
    }
    const handleDownload = async ()=>{
        if (cardRef.current) {
            try {
                const { width } = cardRef.current.getBoundingClientRect();
                const scrollHeight = cardRef.current.scrollHeight;
                const dataUrl = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html$2d$to$2d$image$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toJpeg"])(cardRef.current, {
                    quality: 1.0,
                    pixelRatio: 2,
                    backgroundColor: '#ffffff',
                    canvasWidth: width,
                    canvasHeight: scrollHeight,
                    style: {
                        margin: "0",
                        padding: "35px 0px 0px 20px  "
                    }
                });
                const link = document.createElement('a');
                link.href = dataUrl;
                link.download = isWeekly ? 'uest-weekly-quiz-result.jpg' : 'uest-daily-quiz-result.jpg';
                link.click();
            } catch (error) {
                console.error('Failed to download card:', error);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Failed to download the quiz result card. Please try again.');
            }
        }
    };
    if (isQuizCompleted) {
        const shareUrl = "https://uest.in/mock-exam-card";
        const shareText = `I scored ${calculateScore}/${questions.length} with a streak of ${streakData?.streak || 0} ${isWeekly ? 'weeks' : 'days'} on U-whiz ${isWeekly ? 'Weekly' : 'Daily'} Daily Exam! 🎉 Try it out!`;
        const studentData = localStorage.getItem('student_data');
        const parsedStudentData = studentData ? JSON.parse(studentData) : null;
        const studentName = parsedStudentData ? `${parsedStudentData.firstName} ${parsedStudentData.lastName}` : 'Unknown Student';
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative z-10 w-full max-w-md text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        ref: cardRef,
                        className: "bg-white dark:bg-[#1f2937] rounded-3xl shadow-2xl border border-orange-100 dark:border-gray-700 p-6 mx-auto max-w-md",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-6 text-center text-sm text-gray-600 dark:text-gray-400 flex flex-col items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: "Powered by"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                        lineNumber: 863,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        src: "/logo.png",
                                        alt: "Preply Logo",
                                        width: 120,
                                        height: 40
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                        lineNumber: 864,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                lineNumber: 862,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute inset-0 z-0 pointer-events-none",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "animate-pulse opacity-20 bg-[radial-gradient(#facc15_1px,transparent_1px)] bg-[length:20px_20px] w-full h-full"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                    lineNumber: 872,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                lineNumber: 871,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-3xl font-extrabold text-customOrange dark:text-orange-400 mb-4 text-center",
                                children: [
                                    isWeekly ? 'Weekly' : 'Daily',
                                    " Quiz Completed 🎉"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                lineNumber: 874,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-lg font-medium text-gray-700 dark:text-gray-300 mb-2 text-center",
                                children: [
                                    "Congratulations, ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-customOrange font-bold",
                                        children: studentName
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                        lineNumber: 876,
                                        columnNumber: 32
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                lineNumber: 875,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-base text-gray-600 dark:text-gray-400 mb-4 text-center",
                                children: "Keep up the momentum. 💪"
                            }, void 0, false, {
                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                lineNumber: 878,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-1/3 mx-auto h-2 bg-gray-200 dark:bg-gray-600 rounded-full mb-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-full bg-customOrange dark:bg-orange-400 rounded-full transition-all duration-500",
                                    style: {
                                        width: `${questions.length > 0 ? calculateScore / questions.length * 100 : 0}%`
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                    lineNumber: 882,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                lineNumber: 881,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-base text-gray-800 dark:text-gray-200 mb-4 text-center",
                                children: [
                                    "Final Score: ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-bold",
                                        children: calculateScore
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                        lineNumber: 888,
                                        columnNumber: 28
                                    }, this),
                                    " / ",
                                    questions.length
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                lineNumber: 887,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-center mb-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative flex flex-col items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute w-24 h-24 rounded-full bg-orange-500 opacity-30 blur-xl animate-ping"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 892,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute w-16 h-16 rounded-full bg-red-500 opacity-20 blur-md animate-pulse"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 893,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute w-12 h-12 rounded-full bg-yellow-300 opacity-40 blur-sm animate-bounce"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 894,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-red-500 flex items-center justify-center shadow-lg animate-burning",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-4xl",
                                                children: "🔥"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                lineNumber: 896,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 895,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "mt-2 text-sm font-semibold text-gray-600 dark:text-gray-400 text-center",
                                            children: streakLoading ? 'Loading Streak...' : streakError ? 'Error Loading Streak' : `🔥 Streak: ${streakData?.streak || 0} ${isWeekly ? 'Weeks' : 'Days'}`
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 898,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                    lineNumber: 891,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                lineNumber: 890,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-gray-800 dark:text-gray-300 space-y-1 mb-6 text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                children: "Coins Earned:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                lineNumber: 904,
                                                columnNumber: 18
                                            }, this),
                                            " ",
                                            calculateCoins + (streakData?.streak || 0)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                        lineNumber: 904,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                children: "Bonus:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                lineNumber: 905,
                                                columnNumber: 18
                                            }, this),
                                            " +",
                                            streakData?.streak || 0,
                                            " for Streak!"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                        lineNumber: 905,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                lineNumber: 903,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 861,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col mt-6 bg-white dark:bg-[#1f2937] rounded-3xl shadow-2xl border border-orange-100 dark:border-gray-700 p-6 mx-auto max-w-xl",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col items-center justify-between gap-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-gray-800 dark:text-gray-300 flex-1",
                                        children: "Ask your friends to join the quiz and get a chance to win some coins!"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                        lineNumber: 911,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "social-media-buttons flex items-center gap-4",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$share$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WhatsappShareButton"], {
                                            url: shareUrl,
                                            title: shareText,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$share$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WhatsappIcon"], {
                                                size: 32,
                                                round: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                lineNumber: 916,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 915,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                        lineNumber: 914,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                lineNumber: 910,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex mt-4 gap-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: handleDownload,
                                        className: "download-button bg-orange-500 text-white px-6 py-3 rounded-lg shadow-md hover:bg-orange-600 min-w-[150px] whitespace-nowrap",
                                        children: "📷 Download Result"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                        lineNumber: 921,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: handleGoHome,
                                        className: "bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold px-6 py-3 rounded-lg shadow-md min-w-[150px] whitespace-nowrap",
                                        children: "🚀 Continue Learning"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                        lineNumber: 927,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                lineNumber: 920,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 909,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                lineNumber: 860,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/mock-test/mockExam.tsx",
            lineNumber: 859,
            columnNumber: 7
        }, this);
    }
    const currentQuestion = questions[currentQuestionIndex];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col min-h-screen bg-gray-100 text-gray-900",
        children: [
            isDialogOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-3/4 md:w-1/2 max-h-[80vh] flex flex-col overflow-y-auto",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-lg sm:text-2xl font-bold mb-4",
                            children: [
                                "Start ",
                                isWeekly ? 'Weekly' : 'Daily',
                                " Quiz"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                            lineNumber: 947,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "font-semibold mb-4 text-sm sm:text-base text-gray-600",
                            children: "Note: This is a mock exam for testing purposes only."
                        }, void 0, false, {
                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                            lineNumber: 948,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1 overflow-y-auto pr-2 mb-4 text-sm sm:text-base",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "font-semibold mb-2",
                                    children: "Instructions (English):"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                    lineNumber: 952,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                    className: "list-disc list-inside mb-4 text-gray-600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "Do not switch tabs during the quiz."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 954,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "Do not use restricted keys (Alt, Ctrl, Tab, Shift, Enter, Function keys)."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 955,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "Do not open Developer Tools."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 956,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "Do not exit full-screen mode."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 957,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "Do not interact with other windows or applications."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 958,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "Do not change the screen or minimize the quiz window."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 959,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "Do not receive or make calls during the quiz."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 960,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "Do not use split screen or floating windows on your device."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 961,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                    lineNumber: 953,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "font-semibold mb-2",
                                    children: "સૂચનાઓ (ગુજરાતી):"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                    lineNumber: 963,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                    className: "list-disc list-inside text-gray-600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "ક્વિઝ દરમિયાન ટેબ બદલશો નહીં."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 965,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "પ્રતિબંધિત કીઓ (ઓલ્ટ, કંટ્રોલ, ટેબ, શિફ્ટ, એન્ટર, ફંક્શન કીઓ) નો ઉપયોગ કરશો નહીં."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 966,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "ડેવલપર ટૂલ્સ ખોલશો નહીં."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 967,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "ક્વિઝ દરમિયાન જમણું-ક્લિક કરશો નહીં."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 968,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "ફુલ-સ્ક્રીન મોડમાંથી બહાર નીકળશો નહીં."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 969,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "અન્ય વિન્ડોઝ અથવા એપ્લિકેશન્સ સાથે સંપર્ક કરશો નહીં."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 970,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "સ્ક્રીન બદલશો નહીં અથવા ક્વિઝ વિન્ડો નાની કરશો નહીં."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 971,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "ક્વિઝ દરમિયાન કૉલ રિસીવ કરશો નહીં અથવા કૉલ કરશો નહીં."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 972,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "તમારા ડિવાઇસ પર સ્પ્લિટ સ્ક્રીન અથવા ફ્લોટિંગ વિન્ડોઝનો ઉપયોગ કરશો નહીં."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 973,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                    lineNumber: 964,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                            lineNumber: 951,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                            onClick: handleStartQuiz,
                            className: "bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",
                            children: [
                                "Start ",
                                isWeekly ? 'Weekly' : 'Daily',
                                " Quiz"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                            lineNumber: 976,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                    lineNumber: 946,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                lineNumber: 945,
                columnNumber: 9
            }, this),
            showWarning && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-lg sm:text-2xl font-bold mb-4 text-customOrange",
                            children: "Warning"
                        }, void 0, false, {
                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                            lineNumber: 989,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mb-4 text-sm sm:text-base text-gray-600",
                            children: "You have performed a restricted action. Repeating this will terminate the quiz."
                        }, void 0, false, {
                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                            lineNumber: 990,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                            onClick: ()=>setShowWarning(false),
                            className: "bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",
                            children: "OK"
                        }, void 0, false, {
                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                            lineNumber: 993,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                    lineNumber: 988,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                lineNumber: 987,
                columnNumber: 9
            }, this),
            showTermination && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-lg sm:text-2xl font-bold mb-4 text-red-500",
                            children: "Quiz Terminated"
                        }, void 0, false, {
                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                            lineNumber: 1006,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mb-4 text-sm sm:text-base text-gray-600",
                            children: terminationMessage || "Your quiz has been terminated due to multiple cheating attempts."
                        }, void 0, false, {
                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                            lineNumber: 1007,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                            onClick: handleGoHome,
                            className: "bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 text-sm sm:text-base w-full transition-all",
                            children: "Go to Home"
                        }, void 0, false, {
                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                            lineNumber: 1010,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                    lineNumber: 1005,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                lineNumber: 1004,
                columnNumber: 9
            }, this),
            !isDialogOpen && !isLoginDialogOpen && !isProfileDialogOpen && !isExamTakenDialogOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(QuizHeader, {}, void 0, false, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 1022,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "fixed top-[60px] sm:top-[80px] left-0 right-0 z-10 w-full h-1.5 bg-gray-200",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "h-3.5 bg-customOrange rounded-r-full transition-all duration-300",
                            style: {
                                width: `${progress}%`
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                            lineNumber: 1025,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 1024,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 flex flex-col items-center justify-center px-4 sm:px-6 pt-[80px] sm:pt-[100px] pb-[48px] sm:pb-[64px] min-h-screen",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col items-center justify-center w-full max-w-3xl",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-2 sm:mt-4 mb-4 sm:mb-6 flex items-center gap-2 bg-gray-800/80 px-4 sm:px-6 py-2 rounded-full shadow-lg",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                            className: "w-5 h-5 sm:w-6 sm:h-6 text-customOrange animate-pulse"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 1033,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-lg sm:text-2xl font-bold text-customOrange",
                                            children: formatTime(timeLeft)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 1034,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                    lineNumber: 1032,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-full text-center flex flex-col items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-center mb-3 sm:mb-4",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-xs sm:text-base font-semibold text-customOrange bg-orange-100 px-2 sm:px-3 py-1 rounded-full shadow-sm",
                                                children: [
                                                    "Question ",
                                                    currentQuestionIndex + 1,
                                                    " of ",
                                                    questions.length
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                lineNumber: 1040,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 1039,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-white p-4 sm:p-8 rounded-lg shadow-xl mb-6 w-full max-h-[60vh] sm:max-h-[70vh] overflow-y-auto",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                    className: "text-lg sm:text-2xl md:text-3xl font-bold text-gray-800 mb-4 sm:mb-6",
                                                    children: currentQuestion.question
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                    lineNumber: 1045,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 w-full",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                            variant: "outline",
                                                            className: getButtonClass("optionOne"),
                                                            onClick: ()=>handleOptionClick("optionOne"),
                                                            disabled: showTermination || showAnswerFeedback,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",
                                                                    children: "A"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                                    lineNumber: 1055,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "flex-1 text-left whitespace-normal break-words",
                                                                    children: currentQuestion.optionOne
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                                    lineNumber: 1058,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                            lineNumber: 1049,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                            variant: "outline",
                                                            className: getButtonClass("optionTwo"),
                                                            onClick: ()=>handleOptionClick("optionTwo"),
                                                            disabled: showTermination || showAnswerFeedback,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",
                                                                    children: "B"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                                    lineNumber: 1066,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "flex-1 text-left whitespace-normal break-words",
                                                                    children: currentQuestion.optionTwo
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                                    lineNumber: 1069,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                            lineNumber: 1060,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                            variant: "outline",
                                                            className: getButtonClass("optionThree"),
                                                            onClick: ()=>handleOptionClick("optionThree"),
                                                            disabled: showTermination || showAnswerFeedback,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",
                                                                    children: "C"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                                    lineNumber: 1077,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "flex-1 text-left whitespace-normal break-words",
                                                                    children: currentQuestion.optionThree
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                                    lineNumber: 1080,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                            lineNumber: 1071,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                            variant: "outline",
                                                            className: getButtonClass("optionFour"),
                                                            onClick: ()=>handleOptionClick("optionFour"),
                                                            disabled: showTermination || showAnswerFeedback,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",
                                                                    children: "D"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                                    lineNumber: 1088,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "flex-1 text-left whitespace-normal break-words",
                                                                    children: currentQuestion.optionFour
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                                    lineNumber: 1091,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                            lineNumber: 1082,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                                    lineNumber: 1048,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 1044,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                            className: "bg-customOrange text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg font-semibold shadow-lg transform hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed",
                                            onClick: ()=>handleNextQuestion(),
                                            disabled: showTermination || showAnswerFeedback,
                                            children: currentQuestionIndex === questions.length - 1 ? "Finish" : "Next Question"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 1095,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                    lineNumber: 1038,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
                                    className: "fixed bottom-0 left-0 right-0 bg-black text-white py-2 px-4 sm:px-6 flex items-center justify-center gap-1.5 sm:gap-2 text-xs sm:text-sm",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: "Powered by"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 1104,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "font-semibold",
                                            children: "UEST EdTech"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                            lineNumber: 1105,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/mock-test/mockExam.tsx",
                                    lineNumber: 1103,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/mock-test/mockExam.tsx",
                            lineNumber: 1031,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/mock-test/mockExam.tsx",
                        lineNumber: 1030,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/mock-test/mockExam.tsx",
        lineNumber: 943,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__a64c33e2._.js.map