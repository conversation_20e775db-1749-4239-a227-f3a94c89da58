import { Request, Response } from 'express';
import { UserType } from '@prisma/client';
import {
  registerFcmToken,
  getUserFcmTokens,
  deactivateFcmToken,
  removeFcmToken
} from '@/services/fcmTokenService';
import { sendSuccess, sendError } from '@/utils/response';

export const registerFcmTokenController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token, deviceId, platform } = req.body;

    let userId: string;
    let userType: UserType;

    if ((req as any).class?.id) {
      userId = (req as any).class.id;
      userType = UserType.CLASS;
    } else {
      sendError(res, 'User not authenticated', 401);
      return;
    }

    if (!token) {
      sendError(res, 'FCM token is required', 400);
      return;
    }

    const fcmToken = await registerFcmToken({
      userId,
      userType,
      token,
      deviceId,
      platform
    });

    sendSuccess(res, fcmToken, 'FCM token registered successfully');
  } catch (error) {
    console.error('Error registering FCM token:', error);
    sendError(res, 'Failed to register FCM token', 500);
  }
};


export const getUserFcmTokensController = async (req: Request, res: Response): Promise<void> => {
  try {
    let userId: string;
    let userType: UserType;

    if ((req as any).class?.id) {
      userId = (req as any).class.id;
      userType = UserType.CLASS;
    } else {
      sendError(res, 'User not authenticated', 401);
      return;
    }

    const tokens = await getUserFcmTokens(userId, userType);

    sendSuccess(res, tokens, 'FCM tokens retrieved successfully');
  } catch (error) {
    console.error('Error fetching FCM tokens:', error);
    sendError(res, 'Failed to fetch FCM tokens', 500);
  }
};


export const deactivateFcmTokenController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token } = req.body;

    if (!token) {
      sendError(res, 'FCM token is required', 400);
      return;
    }

    await deactivateFcmToken(token);

    sendSuccess(res, null, 'FCM token deactivated successfully');
  } catch (error) {
    console.error('Error deactivating FCM token:', error);
    sendError(res, 'Failed to deactivate FCM token', 500);
  }
};

export const removeFcmTokenController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token } = req.body;

    if (!token) {
      sendError(res, 'FCM token is required', 400);
      return;
    }

    await removeFcmToken(token);

    sendSuccess(res, null, 'FCM token removed successfully');
  } catch (error) {
    console.error('Error removing FCM token:', error);
    sendError(res, 'Failed to remove FCM token', 500);
  }
};


export const registerStudentFcmTokenController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token, deviceId, platform } = req.body;

    if (!(req as any).student?.id) {
      sendError(res, 'Student not authenticated', 401);
      return;
    }

    const userId = (req as any).student.id;

    if (!token) {
      sendError(res, 'FCM token is required', 400);
      return;
    }

    const fcmToken = await registerFcmToken({
      userId,
      userType: UserType.STUDENT,
      token,
      deviceId,
      platform
    });

    sendSuccess(res, fcmToken, 'FCM token registered successfully');
  } catch (error) {
    console.error('Error registering student FCM token:', error);
    sendError(res, 'Failed to register FCM token', 500);
  }
};

export const registerAdminFcmTokenController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token, deviceId, platform } = req.body;

    if (!(req as any).admin?.id) {
      sendError(res, 'Admin not authenticated', 401);
      return;
    }

    const userId = (req as any).admin.id;

    if (!token) {
      sendError(res, 'FCM token is required', 400);
      return;
    }

    const fcmToken = await registerFcmToken({
      userId,
      userType: UserType.ADMIN,
      token,
      deviceId,
      platform
    });

    sendSuccess(res, fcmToken, 'FCM token registered successfully');
  } catch (error) {
    console.error('Error registering admin FCM token:', error);
    sendError(res, 'Failed to register FCM token', 500);
  }
};
