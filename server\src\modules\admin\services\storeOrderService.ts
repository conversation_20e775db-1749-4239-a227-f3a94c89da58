import prisma from '@/config/prismaClient';
import { createNotification } from '@/utils/notifications';
import { delhiveryService } from '@/services/delhiveryService';

const getBuyerInfo = async (modelId: string, modelType: string) => {
  let buyerName = 'Unknown';
  let buyerEmail = null;
  let buyerAddress = null;

  if (modelType === 'STUDENT') {
    const student = await prisma.student.findUnique({
      where: { id: modelId },
      select: {
        firstName: true,
        lastName: true,
        email: true,
        profile: {
          select: {
            address: true
          }
        }
      }
    });
    if (student) {
      buyerName = `${student.firstName} ${student.lastName}`;
      buyerEmail = student.email;
      buyerAddress = student.profile?.address || null;
    }
  } else if (modelType === 'CLASS') {
    const classUser = await prisma.classes.findUnique({
      where: { id: modelId },
      select: {
        firstName: true,
        lastName: true,
        email: true,
        address: {
          select: {
            fullAddress: true,
            city: true,
            state: true,
            postcode: true,
            country: true
          }
        }
      }
    });
    if (classUser) {
      buyerName = `${classUser.firstName} ${classUser.lastName}`;
      buyerEmail = classUser.email;
      if (classUser.address) {
        const addressParts = [
          classUser.address.fullAddress,
          classUser.address.city,
          classUser.address.state,
          classUser.address.postcode,
          classUser.address.country
        ].filter(Boolean);
        buyerAddress = addressParts.join(', ');
      }
    }
  }

  return { buyerName, buyerEmail, buyerAddress };
};

const createShippingOrder = async (order: any, buyerInfo: any) => {
  try {
    if (!buyerInfo.buyerAddress) {
      console.warn(`No address available for order ${order.id}, skipping shipping creation`);
      return {
        success: false,
        error: 'No address available for shipping'
      };
    }

    let buyerPhone = '';
    if (order.modelType === 'STUDENT') {
      const student = await prisma.student.findUnique({
        where: { id: order.modelId },
        select: { contact: true }
      });
      buyerPhone = student?.contact || '';
    } else if (order.modelType === 'CLASS') {
      const classUser = await prisma.classes.findUnique({
        where: { id: order.modelId },
        select: { contactNo: true }
      });
      buyerPhone = classUser?.contactNo || '';
    }

    if (!buyerPhone || buyerPhone.length < 10) {
      console.warn(`No valid phone number for order ${order.id}, skipping shipping creation`);
      return {
        success: false,
        error: 'No valid phone number available for shipping'
      };
    }

    const cleanPhone = buyerPhone.replace(/\D/g, '').slice(-10);

    if (cleanPhone.length !== 10) {
      console.warn(`Invalid phone number format for order ${order.id}: ${buyerPhone}`);
      return {
        success: false,
        error: 'Invalid phone number format'
      };
    }

    const shippingResult = await delhiveryService.createShippingFromStoreOrder({
      orderId: order.id,
      buyerName: buyerInfo.buyerName,
      buyerPhone: cleanPhone,
      buyerAddress: buyerInfo.buyerAddress,
      itemName: order.itemName,
      quantity: order.quantity,
      totalAmount: order.totalCoins
    });

    if (shippingResult.success && shippingResult.waybill) {
      await prisma.storeOrder.update({
        where: { id: order.id },
        data: {
          waybill: shippingResult.waybill,
          shippingStatus: 'SHIPPED',
          shippingError: null
        }
      });

      console.log(`Shipping order created successfully for order ${order.id}, waybill: ${shippingResult.waybill}`);

      return {
        success: true,
        waybill: shippingResult.waybill
      };
    } else {
      await prisma.storeOrder.update({
        where: { id: order.id },
        data: {
          shippingStatus: 'FAILED',
          shippingError: shippingResult.error || 'Unknown shipping error'
        }
      });

      console.error(`Failed to create shipping order for ${order.id}:`, shippingResult.error);

      return {
        success: false,
        error: shippingResult.error
      };
    }
  } catch (error: any) {
    console.error(`Error creating shipping order for ${order.id}:`, error);

    try {
      await prisma.storeOrder.update({
        where: { id: order.id },
        data: {
          shippingStatus: 'FAILED',
          shippingError: error.message || 'Unknown error during shipping creation'
        }
      });
    } catch (updateError) {
      console.error('Failed to update order with shipping error:', updateError);
    }

    return {
      success: false,
      error: error.message || 'Unknown error during shipping creation'
    };
  }
};

export const getAllStoreOrders = async (filters?: {
  status?: string;
  search?: string;
  startDate?: string;
  endDate?: string;
  modelType?: string;
  page?: number;
  limit?: number;
}) => {
  try {
    const where: any = {};

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.modelType) {
      where.modelType = filters.modelType;
    }

    if (filters?.startDate || filters?.endDate) {
      where.createdAt = {};
      if (filters.startDate) {
        where.createdAt.gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        where.createdAt.lte = new Date(filters.endDate);
      }
    }

    if (filters?.search) {
      const searchTerm = filters.search.toLowerCase();
      where.OR = [
        { id: { contains: searchTerm, mode: 'insensitive' } },
        { modelId: { contains: searchTerm, mode: 'insensitive' } },
        { itemName: { contains: searchTerm, mode: 'insensitive' } }
      ];
    }

    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const skip = (page - 1) * limit;

    const totalCount = await prisma.storeOrder.count({ where });

    const orders = await prisma.storeOrder.findMany({
      where,
      include: {
        item: {
          select: {
            id: true,
            name: true,
            description: true,
            coinPrice: true,
            totalStock: true,
            availableStock: true,
            category: true,
            image: true,
            status: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    });

    const ordersWithBuyerInfo = await Promise.all(
      orders.map(async (order) => {
        const { buyerName, buyerEmail, buyerAddress } = await getBuyerInfo(order.modelId, order.modelType);

        return {
          ...order,
          buyerName,
          buyerEmail,
          buyerAddress
        };
      })
    );

    const totalPages = Math.ceil(totalCount / limit);

    return {
      orders: ordersWithBuyerInfo,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };

  } catch (error: any) {
    console.error('Error fetching store orders:', error.message);
  }
};

export const getStoreOrderById = async (orderId: string) => {
  try {
    const order = await prisma.storeOrder.findUnique({
      where: { id: orderId },
      include: {
        item: {
          select: {
            id: true,
            name: true,
            description: true,
            coinPrice: true,
            totalStock: true,
            availableStock: true,
            category: true,
            image: true,
            status: true
          }
        }
      }
    });

    if (!order) {
      throw new Error('Order not found');
    }

    return order;
  } catch (error: any) {
    console.error('Error fetching order details:', error.message);
  }
};

export const getStoreOrderStats = async () => {
  try {
    const [
      totalOrders,
      completedOrders,
      pendingOrders,
      cancelledOrders,
      totalRevenue,
      todayOrders,
      thisMonthOrders
    ] = await Promise.all([
      prisma.storeOrder.count(),
      prisma.storeOrder.count({ where: { status: 'COMPLETED' } }),
      prisma.storeOrder.count({ where: { status: 'PENDING' } }),
      prisma.storeOrder.count({ where: { status: 'CANCELLED' } }),
      prisma.storeOrder.aggregate({
        where: { status: 'COMPLETED' },
        _sum: { totalCoins: true }
      }),
      prisma.storeOrder.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      }),
      prisma.storeOrder.count({
        where: {
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      })
    ]);

    return {
      totalOrders,
      completedOrders,
      pendingOrders,
      cancelledOrders,
      totalRevenue: totalRevenue._sum.totalCoins || 0,
      todayOrders,
      thisMonthOrders
    };
  } catch (error: any) {
    console.error('Error fetching order statistics:', error.message);
  }
};

export const updateOrderStatus = async (orderId: string, status: string) => {
  try {
    const validStatuses = ['PENDING', 'COMPLETED', 'CANCELLED'];
    if (!validStatuses.includes(status)) {
      throw new Error('Invalid order status');
    }

    const result = await prisma.$transaction(async (tx) => {
      const currentOrder = await tx.storeOrder.findUnique({
        where: { id: orderId },
        include: { item: true }
      });

      if (!currentOrder) {
        throw new Error('Order not found');
      }

      const previousStatus = currentOrder.status;

      const updatedOrder = await tx.storeOrder.update({
        where: { id: orderId },
        data: { status: status as any }
      });

      if (previousStatus !== status) {
        await handleStatusChange(tx, currentOrder, previousStatus, status);
      }

      if (status === 'COMPLETED' && previousStatus !== 'COMPLETED') {
        const { buyerName, buyerEmail, buyerAddress } = await getBuyerInfo(currentOrder.modelId, currentOrder.modelType);

        const delhiveryToken = process.env.DELHIVERY_API_TOKEN;
        if (delhiveryToken && delhiveryToken !== '286366002b94c31e8b9667cf6f306c32f007f859' && delhiveryToken.length > 10) {
          setImmediate(async () => {
            await createShippingOrder(currentOrder, { buyerName, buyerEmail, buyerAddress });
          });
        } else {
          console.log(`Delhivery integration disabled - no valid token configured for order ${currentOrder.id}`);
        }
      }

      return updatedOrder;
    });

    try {
      const order = result;
      let notificationType: string;
      let title: string;
      let message: string;

      if (status === 'COMPLETED') {
        notificationType = order.modelType === 'STUDENT' ? 'STUDENT_STORE_ORDER_APPROVED' : 'CLASS_STORE_ORDER_APPROVED';
        title = 'Store Order Approved! 🎉';
        message = `Great news! Your order for ${order.itemName} has been approved and completed. Order ID: ${order.id.slice(-8)}`;
      } else if (status === 'CANCELLED') {
        notificationType = order.modelType === 'STUDENT' ? 'STUDENT_STORE_ORDER_REJECTED' : 'CLASS_STORE_ORDER_REJECTED';
        title = 'Store Order Cancelled';
        message = `Your order for ${order.itemName} has been cancelled. Your coins have been refunded. Order ID: ${order.id.slice(-8)}`;
      } else {
        notificationType = order.modelType === 'STUDENT' ? 'STUDENT_STORE_PURCHASE' : 'CLASS_STORE_PURCHASE';
        title = 'Store Order Status Updated';
        message = `Your order for ${order.itemName} status has been updated to ${status}. Order ID: ${order.id.slice(-8)}`;
      }

      if (order.modelType === 'STUDENT' || order.modelType === 'CLASS') {
        await createNotification({
          userId: order.modelId,
          userType: order.modelType as 'STUDENT' | 'CLASS',
          type: notificationType as any,
          title,
          message,
          data: {
            orderId: order.id,
            itemName: order.itemName,
            quantity: order.quantity,
            totalCoins: order.totalCoins,
            newStatus: status
          }
        });
      }

      console.log(`Status change notification sent to ${order.modelType} ${order.modelId}: ${order.id} -> ${status}`);
    } catch (error) {
      console.error('Error sending status change notification:', error);
    }

    return result;
  } catch (error: any) {
    console.error('Error updating order status:', error.message);
  }
};

const handleStatusChange = async (tx: any, order: any, previousStatus: string, newStatus: string) => {
  const { modelId, modelType, itemId, quantity, totalCoins } = order;

  if (previousStatus === 'PENDING') {
    if (newStatus === 'COMPLETED') {
      await tx.storeItem.update({
        where: { id: itemId },
        data: {
          availableStock: {
            decrement: quantity
          }
        }
      });
    } else if (newStatus === 'CANCELLED') {
      await tx.uestCoins.update({
        where: {
          modelId_modelType: {
            modelId: modelId,
            modelType: modelType
          }
        },
        data: {
          coins: {
            increment: totalCoins
          }
        }
      });

      await tx.uestCoinTransaction.create({
        data: {
          modelId: modelId,
          modelType: modelType,
          amount: totalCoins,
          type: 'CREDIT',
          reason: `Store order cancelled - Order #${order.id.slice(-8)}`
        }
      });
    }
  }

  else if (previousStatus === 'COMPLETED') {
    if (newStatus === 'CANCELLED') {
      await updateCoinsAndStock(tx, modelId, modelType, itemId, quantity, totalCoins, order.id, 'REFUND');
    } else if (newStatus === 'PENDING') {
      await tx.storeItem.update({
        where: { id: itemId },
        data: {
          availableStock: {
            increment: quantity
          }
        }
      });
    }
  }

  else if (previousStatus === 'CANCELLED') {
    if (newStatus === 'COMPLETED') {
      await updateCoinsAndStock(tx, modelId, modelType, itemId, quantity, totalCoins, order.id, 'DEDUCT');
    } else if (newStatus === 'PENDING') {
      await tx.uestCoins.update({
        where: {
          modelId_modelType: {
            modelId: modelId,
            modelType: modelType
          }
        },
        data: {
          coins: {
            decrement: totalCoins
          }
        }
      });

      await tx.uestCoinTransaction.create({
        data: {
          modelId: modelId,
          modelType: modelType,
          amount: totalCoins,
          type: 'DEBIT',
          reason: `Store order reactivated - Order #${order.id.slice(-8)}`
        }
      });
    }
  }
};

const updateCoinsAndStock = async (
  tx: any,
  modelId: string,
  modelType: string,
  itemId: string,
  quantity: number,
  totalCoins: number,
  orderId: string,
  operation: 'DEDUCT' | 'REFUND'
) => {
  const isDeduct = operation === 'DEDUCT';

  await tx.uestCoins.update({
    where: {
      modelId_modelType: {
        modelId: modelId,
        modelType: modelType
      }
    },
    data: {
      coins: {
        [isDeduct ? 'decrement' : 'increment']: totalCoins
      }
    }
  });

  await tx.uestCoinTransaction.create({
    data: {
      modelId: modelId,
      modelType: modelType,
      amount: totalCoins,
      type: isDeduct ? 'DEBIT' : 'CREDIT',
      reason: `Store ${isDeduct ? 'purchase completed' : 'order refund'} - Order #${orderId.slice(-8)}`
    }
  });

  await tx.storeItem.update({
    where: { id: itemId },
    data: {
      availableStock: {
        [isDeduct ? 'decrement' : 'increment']: quantity
      }
    }
  });
};
