import axios from 'axios';
import { z } from 'zod';

const DelhiveryShipmentSchema = z.object({
  name: z.string().min(1, 'Consignee name is required'),
  add: z.string().min(1, 'Address is required'),
  pin: z.string().regex(/^\d{6}$/, 'PIN must be 6 digits'),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  country: z.string().default('India'),
  phone: z.string().regex(/^\d{10}$/, 'Phone must be 10 digits'),
  order: z.string().min(1, 'Order ID is required'),
  payment_mode: z.enum(['Prepaid', 'COD']).default('Prepaid'),
  products_desc: z.string().default(''),
  total_amount: z.string().default(''),
  quantity: z.string().default('1'),
  weight: z.string().default('0.5'), 
  shipment_width: z.string().default('10'),
  shipment_height: z.string().default('10'),
  shipping_mode: z.enum(['Surface', 'Express']).default('Surface'),
  address_type: z.string().default('home')
});

const DelhiveryRequestSchema = z.object({
  shipments: z.array(DelhiveryShipmentSchema),
  pickup_location: z.object({
    name: z.string().min(1, 'Pickup location name is required')
  })
});

export interface DelhiveryShipmentData {
  name: string;
  add: string;
  pin: string;
  city: string;
  state: string;
  country?: string;
  phone: string;
  order: string;
  payment_mode?: 'Prepaid' | 'COD';
  products_desc?: string;
  total_amount?: string;
  quantity?: string;
  weight?: string;
  shipment_width?: string;
  shipment_height?: string;
  shipping_mode?: 'Surface' | 'Express';
  address_type?: string;
}

export interface DelhiveryResponse {
  success: boolean;
  packages?: Array<{
    waybill: string;
    status: string;
    remarks: string;
  }>;
  rmk?: string;
  cash_pickups_count?: number;
  package_count?: number;
  upload_wbn?: string;
}

export interface CreateShippingOrderResult {
  success: boolean;
  waybill?: string;
  error?: string;
  response?: DelhiveryResponse;
}

class DelhiveryService {
  private readonly baseUrl: string;
  private readonly token: string;
  private readonly pickupLocation: string;

  constructor() {
    this.baseUrl = process.env.DELHIVERY_BASE_URL || 'https://staging-express.delhivery.com';
    this.token = process.env.DELHIVERY_API_TOKEN || '286366002b94c31e8b9667cf6f306c32f007f859';
    this.pickupLocation = process.env.DELHIVERY_PICKUP_LOCATION || 'Uest Edtech Pvt Ltd';

    if (!this.token) {
      console.warn('DELHIVERY_API_TOKEN not configured. Delhivery integration will not work.');
    }
  }

  async createShippingOrder(shipmentData: DelhiveryShipmentData): Promise<CreateShippingOrderResult> {
    try {
      if (!this.token) {
        console.error('Delhivery API token not configured');
        return {
          success: false,
          error: 'Delhivery API token not configured'
        };
      }

      console.log('Delhivery API Configuration:', {
        baseUrl: this.baseUrl,
        tokenLength: this.token.length,
        tokenPrefix: this.token.substring(0, 10) + '...'
      });

      const validatedShipment = DelhiveryShipmentSchema.parse(shipmentData);

      const requestData = {
        shipments: [validatedShipment],
        pickup_location: {
          name: this.pickupLocation
        }
      };

      DelhiveryRequestSchema.parse(requestData);

      const formData = new URLSearchParams();
      formData.append('format', 'json');
      formData.append('data', JSON.stringify(requestData));

      const options = {
        method: 'POST',
        url: `${this.baseUrl}/api/cmu/create.json`,
        headers: {
          'Authorization': `Token ${this.token}`,
          'Accept': 'application/json',
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'UEST-Store-Integration/1.0'
        },
        data: formData.toString()
      };

      console.log('Delhivery Request Details:', {
        url: options.url,
        method: options.method,
        headers: options.headers,
        dataLength: options.data.length,
        dataPreview: options.data.substring(0, 200) + '...'
      });

      console.log('Creating Delhivery shipping order:', {
        order: shipmentData.order,
        name: shipmentData.name,
        city: shipmentData.city
      });

      let response;
      try {
        response = await axios.request(options);
      } catch (authError: any) {
        if (authError.response?.status === 401) {
          console.log('First auth attempt failed, trying alternative format...');

          const altOptions = {
            ...options,
            headers: {
              ...options.headers,
              'Authorization': this.token,
            }
          };

          console.log('Trying alternative auth format:', altOptions.headers.Authorization.substring(0, 10) + '...');
          response = await axios.request(altOptions);
        } else {
          throw authError;
        }
      }
      const delhiveryResponse: DelhiveryResponse = response.data;

      if (delhiveryResponse.success && delhiveryResponse.packages && delhiveryResponse.packages.length > 0) {
        const waybill = delhiveryResponse.packages[0].waybill;
        
        console.log('Delhivery shipping order created successfully:', {
          order: shipmentData.order,
          waybill: waybill
        });

        return {
          success: true,
          waybill: waybill,
          response: delhiveryResponse
        };
      } else {
        console.error('Delhivery order creation failed:', delhiveryResponse);
        return {
          success: false,
          error: delhiveryResponse.rmk || 'Failed to create shipping order',
          response: delhiveryResponse
        };
      }

    } catch (error: any) {
      console.error('Error creating Delhivery shipping order:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers
        }
      });

      if (error.response?.status === 401) {
        return {
          success: false,
          error: 'Delhivery API authentication failed. Please check your API token.',
          response: error.response.data
        };
      }

      if (error.response?.data) {
        return {
          success: false,
          error: `Delhivery API error (${error.response.status}): ${JSON.stringify(error.response.data)}`,
          response: error.response.data
        };
      }

      return {
        success: false,
        error: error.message || 'Unknown error occurred while creating shipping order'
      };
    }
  }

  private extractPinCode(address: string): string | null {
    const pinMatch = address.match(/\b\d{6}\b/);
    return pinMatch ? pinMatch[0] : null;
  }

  private extractCity(address: string): string {
    const parts = address.split(',').map(part => part.trim());
    return parts.length > 1 ? parts[parts.length - 2] : parts[0];
  }

  private extractState(address: string): string {
    const parts = address.split(',').map(part => part.trim());
    return parts.length > 2 ? parts[parts.length - 3] : 'Unknown';
  }

  parseAddress(address: string): { add: string; pin: string; city: string; state: string } {
    const pin = this.extractPinCode(address) || '000000';
    const city = this.extractCity(address);
    const state = this.extractState(address);

    return {
      add: address,
      pin: pin,
      city: city,
      state: state
    };
  }

  async createShippingFromStoreOrder(orderData: {
    orderId: string;
    buyerName: string;
    buyerPhone: string;
    buyerAddress: string;
    itemName: string;
    quantity: number;
    totalAmount: number;
  }): Promise<CreateShippingOrderResult> {
    try {
      const addressComponents = this.parseAddress(orderData.buyerAddress);
      
      const shipmentData: DelhiveryShipmentData = {
        name: orderData.buyerName,
        add: addressComponents.add,
        pin: addressComponents.pin,
        city: addressComponents.city,
        state: addressComponents.state,
        country: 'India',
        phone: orderData.buyerPhone,
        order: orderData.orderId,
        payment_mode: 'Prepaid',
        products_desc: orderData.itemName,
        total_amount: orderData.totalAmount.toString(),
        quantity: orderData.quantity.toString(),
        weight: '0.5', // Default weight - you might want to make this configurable
        shipping_mode: 'Surface'
      };

      return await this.createShippingOrder(shipmentData);
    } catch (error: any) {
      console.error('Error creating shipping from store order:', error);
      return {
        success: false,
        error: error.message || 'Failed to create shipping order from store order data'
      };
    }
  }
}

export const delhiveryService = new DelhiveryService();
