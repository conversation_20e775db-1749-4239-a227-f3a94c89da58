import { Request, Response } from 'express';
import { sendError, sendSuccess } from '@/utils/response';
import {
  createBankPaymentDetails,
  updateBankPaymentDetails,
  findBankPaymentByClassId,
  findBankPaymentByIdAndClassId,
  BankPaymentData
} from '../services/classesBankPaymentDetailService';
import { createBankPaymentSchema, updateBankPaymentSchema } from '../requests/classesBankPaymentDetailRequest';

export const createBankPayment = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    try {
      const validatedData = createBankPaymentSchema.parse(req.body);

      const existingPayment = await findBankPaymentByClassId(classId);

      const bankPaymentData: BankPaymentData = validatedData;

      let bankPayment;

      if (existingPayment) {
        bankPayment = await updateBankPaymentDetails(existingPayment.id, bankPaymentData);
      } else {
        bankPayment = await createBankPaymentDetails(classId, bankPaymentData);
      }

      return sendSuccess(res, bankPayment, 'Bank payment details saved successfully');
    } catch (error: any) {
      console.error('Error saving bank payment details:', error);
      return sendError(res, 'Internal server error', 500);
    }
  } catch (error) {
    console.error('Error saving bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

export const updateBankPayment = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;
    const { id } = req.params;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    if (!id) {
      return sendError(res, 'Payment ID is required', 400);
    }

    try {
      const validatedData = updateBankPaymentSchema.parse(req.body);

      const existingPayment = await findBankPaymentByIdAndClassId(id, classId);

      if (!existingPayment) {
        return sendError(res, 'Bank payment details not found or unauthorized', 404);
      }

      const bankPaymentData: BankPaymentData = validatedData;

      const updatedPayment = await updateBankPaymentDetails(id, bankPaymentData);

      return sendSuccess(res, updatedPayment, 'Bank payment details updated successfully');
    } catch (error: any) {
      console.error('Error updating bank payment details:', error);
      return sendError(res, 'Internal server error', 500);
    }
  } catch (error) {
    console.error('Error updating bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

export const getBankPaymentDetails = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    const bankPayment = await findBankPaymentByClassId(classId);

    if (!bankPayment) {
      return sendSuccess(res, null, 'No bank payment details found');
    }

    return sendSuccess(res, bankPayment, 'Bank payment details retrieved successfully');
  } catch (error) {
    console.error('Error fetching bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};