
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.6.0
 * Query Engine version: f676762280b54cd07c770017ed3711ddde35f37a
 */
Prisma.prismaVersion = {
  client: "6.6.0",
  engine: "f676762280b54cd07c770017ed3711ddde35f37a"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.ClassesScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  className: 'className',
  email: 'email',
  createdAt: 'createdAt',
  contactNo: 'contactNo',
  username: 'username',
  isVerified: 'isVerified'
};

exports.Prisma.ClassesAboutScalarFieldEnum = {
  id: 'id',
  birthDate: 'birthDate',
  catchyHeadline: 'catchyHeadline',
  tutorBio: 'tutorBio',
  profilePhoto: 'profilePhoto',
  classesLogo: 'classesLogo',
  videoUrl: 'videoUrl',
  classId: 'classId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AdminUserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  createdAt: 'createdAt'
};

exports.Prisma.ClassesExpereinceScalarFieldEnum = {
  id: 'id',
  classId: 'classId',
  title: 'title',
  certificateUrl: 'certificateUrl',
  from: 'from',
  to: 'to',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isExperience: 'isExperience',
  status: 'status'
};

exports.Prisma.ClassesEducationScalarFieldEnum = {
  id: 'id',
  classId: 'classId',
  university: 'university',
  degree: 'degree',
  degreeType: 'degreeType',
  passoutYear: 'passoutYear',
  certificate: 'certificate',
  createdAt: 'createdAt',
  isDegree: 'isDegree',
  status: 'status'
};

exports.Prisma.ClassesCertificatesScalarFieldEnum = {
  id: 'id',
  classId: 'classId',
  title: 'title',
  certificateUrl: 'certificateUrl',
  createdAt: 'createdAt',
  isCertificate: 'isCertificate',
  status: 'status'
};

exports.Prisma.ClassesStatusScalarFieldEnum = {
  id: 'id',
  classId: 'classId',
  status: 'status',
  createdAt: 'createdAt'
};

exports.Prisma.UestCoinsScalarFieldEnum = {
  id: 'id',
  modelId: 'modelId',
  modelType: 'modelType',
  coins: 'coins',
  createdAt: 'createdAt'
};

exports.Prisma.UestCoinTransactionScalarFieldEnum = {
  id: 'id',
  modelId: 'modelId',
  modelType: 'modelType',
  amount: 'amount',
  type: 'type',
  reason: 'reason',
  createdAt: 'createdAt'
};

exports.Prisma.TuitionClassScalarFieldEnum = {
  id: 'id',
  classId: 'classId',
  education: 'education',
  boardType: 'boardType',
  subject: 'subject',
  medium: 'medium',
  section: 'section',
  coachingType: 'coachingType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  details: 'details'
};

exports.Prisma.ConstantCategoryScalarFieldEnum = {
  id: 'id',
  name: 'name'
};

exports.Prisma.ConstantDetailScalarFieldEnum = {
  id: 'id',
  name: 'name',
  categoryId: 'categoryId'
};

exports.Prisma.ConstantSubDetailScalarFieldEnum = {
  id: 'id',
  name: 'name',
  detailId: 'detailId'
};

exports.Prisma.ConstantSubDetailValueScalarFieldEnum = {
  id: 'id',
  name: 'name',
  isActive: 'isActive',
  subDetailId: 'subDetailId'
};

exports.Prisma.ExamScalarFieldEnum = {
  id: 'id',
  exam_name: 'exam_name',
  start_date: 'start_date',
  duration: 'duration',
  marks: 'marks',
  total_student_intake: 'total_student_intake',
  level: 'level',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  total_questions: 'total_questions',
  coins_required: 'coins_required',
  exam_type: 'exam_type',
  start_registration_date: 'start_registration_date'
};

exports.Prisma.UwhizPriceRankScalarFieldEnum = {
  id: 'id',
  examId: 'examId',
  rank: 'rank',
  price: 'price'
};

exports.Prisma.Question_paperScalarFieldEnum = {
  id: 'id',
  question: 'question',
  optionOne: 'optionOne',
  optionTwo: 'optionTwo',
  optionThree: 'optionThree',
  optionFour: 'optionFour',
  correctAns: 'correctAns',
  examId: 'examId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Question_answerScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  questionId: 'questionId',
  answer: 'answer',
  createdAt: 'createdAt',
  isCorrect: 'isCorrect'
};

exports.Prisma.ExamApplicationScalarFieldEnum = {
  id: 'id',
  examId: 'examId',
  classId: 'classId',
  createdAt: 'createdAt'
};

exports.Prisma.ClassesThoughtScalarFieldEnum = {
  id: 'id',
  classId: 'classId',
  thoughts: 'thoughts',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TestimonialScalarFieldEnum = {
  id: 'id',
  classId: 'classId',
  userId: 'userId',
  message: 'message',
  rating: 'rating',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BlogScalarFieldEnum = {
  id: 'id',
  classId: 'classId',
  blogTitle: 'blogTitle',
  blogImage: 'blogImage',
  blogDescription: 'blogDescription',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StudentScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  contact: 'contact',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isVerified: 'isVerified',
  googleId: 'googleId',
  profilePhoto: 'profilePhoto',
  middleName: 'middleName',
  mothersName: 'mothersName'
};

exports.Prisma.StudentProfileScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  medium: 'medium',
  classroom: 'classroom',
  birthday: 'birthday',
  school: 'school',
  photo: 'photo',
  documentUrl: 'documentUrl',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  address: 'address',
  aadhaarNo: 'aadhaarNo',
  age: 'age',
  birthPlace: 'birthPlace',
  bloodGroup: 'bloodGroup',
  caste: 'caste',
  contactNo2: 'contactNo2',
  gender: 'gender',
  motherTongue: 'motherTongue',
  religion: 'religion',
  subCaste: 'subCaste',
  uidNo: 'uidNo'
};

exports.Prisma.StudentWishlistScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  savedClassId: 'savedClassId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ClassesReviewsScalarFieldEnum = {
  id: 'id',
  modelId: 'modelId',
  modelType: 'modelType',
  classId: 'classId',
  studentId: 'studentId',
  studentName: 'studentName',
  userName: 'userName',
  rating: 'rating',
  message: 'message',
  createdAt: 'createdAt'
};

exports.Prisma.ClassesCanApplyForQuestionBankScalarFieldEnum = {
  id: 'id',
  classId: 'classId',
  hasEligible: 'hasEligible'
};

exports.Prisma.ReferralLinkScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  userType: 'userType',
  code: 'code',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReferralScalarFieldEnum = {
  id: 'id',
  referralLinkId: 'referralLinkId',
  referredUserId: 'referredUserId',
  referredUserType: 'referredUserType',
  referredUserName: 'referredUserName',
  referredUserEmail: 'referredUserEmail',
  createdAt: 'createdAt'
};

exports.Prisma.ReferralEarningScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  examId: 'examId',
  referralId: 'referralId',
  earningType: 'earningType',
  amount: 'amount',
  paymentStatus: 'paymentStatus',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ChatMessageScalarFieldEnum = {
  id: 'id',
  text: 'text',
  senderId: 'senderId',
  senderType: 'senderType',
  recipientId: 'recipientId',
  recipientType: 'recipientType',
  timestamp: 'timestamp',
  isRead: 'isRead'
};

exports.Prisma.ExamMonitoringPhotoScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  examId: 'examId',
  photoUrl: 'photoUrl',
  capturedAt: 'capturedAt',
  createdAt: 'createdAt'
};

exports.Prisma.ClassesAddressScalarFieldEnum = {
  id: 'id',
  fullAddress: 'fullAddress',
  city: 'city',
  state: 'state',
  postcode: 'postcode',
  country: 'country',
  classId: 'classId',
  latitude: 'latitude',
  longitude: 'longitude',
  createdAt: 'createdAt'
};

exports.Prisma.StudentClassViewLogScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  classId: 'classId',
  viewedAt: 'viewedAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  userType: 'userType',
  type: 'type',
  title: 'title',
  message: 'message',
  data: 'data',
  isRead: 'isRead',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OtpMessageScalarFieldEnum = {
  id: 'id',
  contactNo: 'contactNo',
  otp: 'otp',
  createdAt: 'createdAt',
  expiredAt: 'expiredAt',
  requestCount: 'requestCount'
};

exports.Prisma.StoreItemScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  coinPrice: 'coinPrice',
  totalStock: 'totalStock',
  availableStock: 'availableStock',
  category: 'category',
  image: 'image',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StoreOrderScalarFieldEnum = {
  id: 'id',
  modelId: 'modelId',
  modelType: 'modelType',
  itemId: 'itemId',
  itemName: 'itemName',
  itemPrice: 'itemPrice',
  quantity: 'quantity',
  totalCoins: 'totalCoins',
  status: 'status',
  waybill: 'waybill',
  shippingStatus: 'shippingStatus',
  shippingError: 'shippingError',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StoreCartScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  userType: 'userType',
  itemId: 'itemId',
  quantity: 'quantity',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserActivityLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  userType: 'userType',
  activityType: 'activityType',
  createdAt: 'createdAt'
};

exports.Prisma.ClassesBankPaymentDetailsScalarFieldEnum = {
  id: 'id',
  classId: 'classId',
  bankName: 'bankName',
  accountNumber: 'accountNumber',
  reAccountNumber: 'reAccountNumber',
  ifscCode: 'ifscCode',
  accountHolderName: 'accountHolderName',
  branchName: 'branchName',
  upiId: 'upiId',
  defaultMethod: 'defaultMethod',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RazorpayXPayoutMetaScalarFieldEnum = {
  id: 'id',
  classId: 'classId',
  contactId: 'contactId',
  fundAccountId: 'fundAccountId',
  defaultMethod: 'defaultMethod',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FcmTokenScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  userType: 'userType',
  token: 'token',
  deviceId: 'deviceId',
  platform: 'platform',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.Status = exports.$Enums.Status = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.ClassApprovalStatus = exports.$Enums.ClassApprovalStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  IN_PROCESS: 'IN_PROCESS'
};

exports.TransactionType = exports.$Enums.TransactionType = {
  CREDIT: 'CREDIT',
  DEBIT: 'DEBIT'
};

exports.ExamType = exports.$Enums.ExamType = {
  CLASSES: 'CLASSES',
  STUDENTS: 'STUDENTS'
};

exports.UserType = exports.$Enums.UserType = {
  ADMIN: 'ADMIN',
  CLASS: 'CLASS',
  STUDENT: 'STUDENT'
};

exports.EarningType = exports.$Enums.EarningType = {
  REGISTRATION: 'REGISTRATION',
  UWHIZ_APPLICATION: 'UWHIZ_APPLICATION'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PAID: 'PAID',
  UNPAID: 'UNPAID'
};

exports.NotificationType = exports.$Enums.NotificationType = {
  STUDENT_ACCOUNT_CREATED: 'STUDENT_ACCOUNT_CREATED',
  STUDENT_COIN_PURCHASE: 'STUDENT_COIN_PURCHASE',
  STUDENT_UWHIZ_PARTICIPATION: 'STUDENT_UWHIZ_PARTICIPATION',
  STUDENT_PROFILE_APPROVED: 'STUDENT_PROFILE_APPROVED',
  STUDENT_PROFILE_REJECTED: 'STUDENT_PROFILE_REJECTED',
  STUDENT_CHAT_MESSAGE: 'STUDENT_CHAT_MESSAGE',
  STUDENT_STORE_PURCHASE: 'STUDENT_STORE_PURCHASE',
  STUDENT_STORE_ORDER_APPROVED: 'STUDENT_STORE_ORDER_APPROVED',
  STUDENT_STORE_ORDER_REJECTED: 'STUDENT_STORE_ORDER_REJECTED',
  CLASS_ACCOUNT_CREATED: 'CLASS_ACCOUNT_CREATED',
  CLASS_COIN_PURCHASE: 'CLASS_COIN_PURCHASE',
  CLASS_PROFILE_APPROVED: 'CLASS_PROFILE_APPROVED',
  CLASS_PROFILE_REJECTED: 'CLASS_PROFILE_REJECTED',
  CLASS_CHAT_MESSAGE: 'CLASS_CHAT_MESSAGE',
  CLASS_CONTENT_APPROVED: 'CLASS_CONTENT_APPROVED',
  CLASS_CONTENT_REJECTED: 'CLASS_CONTENT_REJECTED',
  CLASS_EDUCATION_ADDED: 'CLASS_EDUCATION_ADDED',
  CLASS_EXPERIENCE_ADDED: 'CLASS_EXPERIENCE_ADDED',
  CLASS_CERTIFICATE_ADDED: 'CLASS_CERTIFICATE_ADDED',
  CLASS_STORE_PURCHASE: 'CLASS_STORE_PURCHASE',
  CLASS_STORE_ORDER_APPROVED: 'CLASS_STORE_ORDER_APPROVED',
  CLASS_STORE_ORDER_REJECTED: 'CLASS_STORE_ORDER_REJECTED',
  ADMIN_NEW_STUDENT_REGISTRATION: 'ADMIN_NEW_STUDENT_REGISTRATION',
  ADMIN_NEW_CLASS_REGISTRATION: 'ADMIN_NEW_CLASS_REGISTRATION',
  ADMIN_PROFILE_REVIEW_REQUIRED: 'ADMIN_PROFILE_REVIEW_REQUIRED',
  ADMIN_CONTENT_REVIEW_REQUIRED: 'ADMIN_CONTENT_REVIEW_REQUIRED',
  ADMIN_NEW_STORE_ORDER: 'ADMIN_NEW_STORE_ORDER',
  EXAM_APPLICATION_SUCCESS: 'EXAM_APPLICATION_SUCCESS',
  NEW_EXAM_APPLICATION: 'NEW_EXAM_APPLICATION'
};

exports.StoreItemStatus = exports.$Enums.StoreItemStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.ModelType = exports.$Enums.ModelType = {
  CLASS: 'CLASS',
  STUDENT: 'STUDENT',
  SCHOOL: 'SCHOOL'
};

exports.StoreOrderStatus = exports.$Enums.StoreOrderStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.PaymentMethod = exports.$Enums.PaymentMethod = {
  BANK: 'BANK',
  UPI: 'UPI'
};

exports.Prisma.ModelName = {
  Classes: 'Classes',
  ClassesAbout: 'ClassesAbout',
  AdminUser: 'AdminUser',
  ClassesExpereince: 'ClassesExpereince',
  ClassesEducation: 'ClassesEducation',
  ClassesCertificates: 'ClassesCertificates',
  ClassesStatus: 'ClassesStatus',
  UestCoins: 'UestCoins',
  UestCoinTransaction: 'UestCoinTransaction',
  TuitionClass: 'TuitionClass',
  ConstantCategory: 'ConstantCategory',
  ConstantDetail: 'ConstantDetail',
  ConstantSubDetail: 'ConstantSubDetail',
  ConstantSubDetailValue: 'ConstantSubDetailValue',
  Exam: 'Exam',
  UwhizPriceRank: 'UwhizPriceRank',
  Question_paper: 'Question_paper',
  Question_answer: 'Question_answer',
  ExamApplication: 'ExamApplication',
  ClassesThought: 'ClassesThought',
  Testimonial: 'Testimonial',
  Blog: 'Blog',
  Student: 'Student',
  StudentProfile: 'StudentProfile',
  StudentWishlist: 'StudentWishlist',
  ClassesReviews: 'ClassesReviews',
  ClassesCanApplyForQuestionBank: 'ClassesCanApplyForQuestionBank',
  ReferralLink: 'ReferralLink',
  Referral: 'Referral',
  ReferralEarning: 'ReferralEarning',
  ChatMessage: 'ChatMessage',
  ExamMonitoringPhoto: 'ExamMonitoringPhoto',
  ClassesAddress: 'ClassesAddress',
  StudentClassViewLog: 'StudentClassViewLog',
  Notification: 'Notification',
  OtpMessage: 'OtpMessage',
  StoreItem: 'StoreItem',
  StoreOrder: 'StoreOrder',
  StoreCart: 'StoreCart',
  UserActivityLog: 'UserActivityLog',
  ClassesBankPaymentDetails: 'ClassesBankPaymentDetails',
  RazorpayXPayoutMeta: 'RazorpayXPayoutMeta',
  FcmToken: 'FcmToken'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
