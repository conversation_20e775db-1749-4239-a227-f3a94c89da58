"use client";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import mockExamImage from "../../../public/mockExamImage.svg";
import { FaRegClipboard, FaClock, FaCoins } from "react-icons/fa6";
import { GiMedal, GiTrophy } from "react-icons/gi";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import MockExamButton from "../mock-test/mockExamButton";
import { BookCheck } from "lucide-react";
import WeeklyExamButton from "../mock-test/weeklyExam";

const Page = () => {
    const router = useRouter();
    let studentId: string | null = null;
    try {
        const data = localStorage.getItem("student_data");
        studentId = data ? JSON.parse(data).id : null;
    } catch (error: any) {
        console.error("Error retrieving studentId:", error);
        studentId = null;
    }

    return (
        <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 text-gray-800 dark:text-gray-100 transition-colors duration-500">
            <Header />

            {/* Hero Section */}
            <section className="bg-black py-12 flex justify-center">
                <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                >
                    <Image
                        height={50}
                        width={200}
                        src={mockExamImage.src}
                        alt="Current Affairs Quiz Logo"
                        priority
                        quality={100}
                        className="object-contain rounded-lg shadow-lg"
                    />
                </motion.div>
            </section>

            {/* Heading */}
            <section className="text-center mt-10 px-4 pt-7">
                <motion.h1
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="text-4xl md:text-5xl font-extrabold tracking-tight text-gray-900 dark:text-white"
                >
                    Daily <span className="text-amber-500">Quiz</span>
                </motion.h1>
                <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="mt-3 text-lg text-gray-600 dark:text-gray-300"
                >
                    Stay informed, test your knowledge, and earn exclusive rewards!
                </motion.p>
                <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.5 }}
                    className="mt-8 mx-auto w-full max-w-3xl bg-white dark:bg-gray-900/70 border border-amber-300 dark:border-amber-700 rounded-2xl px-6 py-5 shadow-lg flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-6"
                >
                    <div className="flex items-center gap-4">
                        <div className="text-3xl">🛍️</div>
                        <p className="text-base sm:text-lg text-gray-800 dark:text-gray-100 font-semibold">
                            The <span className="text-amber-600 dark:text-amber-400">Store</span> is now <strong>LIVE</strong> — redeem your coins for exciting rewards!
                        </p>
                    </div>
                    <Button
                        className="text-white bg-amber-500 hover:bg-amber-600 px-5 py-2 text-sm sm:text-base font-semibold rounded-lg shadow transition"
                        onClick={() => router.push("/store")}
                    >
                        Visit Store
                    </Button>
                </motion.div>
            </section>

            {/* Main Content Section */}
            <section className="flex flex-col justify-center p-4 sm:p-6 md:p-10 lg:p-16 gap-8">

                {/* Weekly Challenge Card */}
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7, delay: 0.8 }}
                >
                    <Card className="max-w-4xl w-full mx-auto bg-white dark:bg-gray-800/90 border border-gray-200/50 dark:border-gray-700/50 rounded-3xl shadow-xl transition-all duration-500 p-8 backdrop-blur-sm">
                        <div className="flex flex-col items-center gap-6">
                            <motion.div
                                className="flex items-center gap-4"
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.6, delay: 0.9 }}
                            >
                                <GiTrophy className="text-amber-500 text-4xl" />
                                <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight text-gray-900 dark:text-white">
                                    Weekly <span className="text-amber-500">Challenge</span>
                                </h2>
                            </motion.div>
                            <motion.p
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 1.0 }}
                                className="text-lg text-gray-700 dark:text-gray-200 text-center"
                            >
                                Test your skills every Sunday for a chance to win big rewards!
                            </motion.p>
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <motion.div
                                    className="flex items-center gap-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 shadow-sm transition-all duration-200"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5, delay: 1.1 }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    <FaClock className="text-amber-500 text-xl" />
                                    <span className="text-base font-semibold"><strong>Every Sunday</strong></span>
                                </motion.div>
                                <motion.div
                                    className="flex items-center gap-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 shadow-sm transition-all duration-200"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5, delay: 1.2 }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    <FaRegClipboard className="text-amber-500 text-xl" />
                                    <span className="text-base font-semibold"><strong>25 Questions</strong></span>
                                </motion.div>
                                <motion.div
                                    className="flex items-center gap-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 shadow-sm transition-all duration-200"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5, delay: 1.3 }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    <FaCoins className="text-amber-500 text-xl flex-shrink-0" />
                                    <span className="flex flex-col items-start text-base font-semibold">
                                        <span className="flex items-center gap-1 text-gray-500 dark:text-gray-400 text-sm line-through">
                                            <motion.div whileHover={{ scale: 1.1 }} transition={{ duration: 0.2 }}>
                                                <Image
                                                    src="/uest_coin.png"
                                                    alt="Coin"
                                                    width={14}
                                                    height={14}
                                                    sizes="(max-width: 640px) 14px, 16px"
                                                    loading="lazy"
                                                    className="inline-block"
                                                />
                                            </motion.div>
                                            9 coins
                                        </span>
                                        <strong className="text-amber-600 dark:text-amber-400 text-lg">Free Entry</strong>
                                    </span>
                                </motion.div>
                                <motion.div
                                    className="flex items-center gap-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 shadow-sm transition-all duration-200"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5, delay: 1.4 }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    <GiMedal className="text-amber-500 text-xl" />
                                    <span className="text-base font-semibold"><strong>Earn Coins</strong></span>
                                </motion.div>
                            </div>
                            <motion.div
                                initial={{ opacity: 0, scale: 0.95 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5, delay: 1.5 }}
                                className="w-full max-w-xs"
                            >
                                <WeeklyExamButton />
                            </motion.div>
                            {/* <motion.div
                                className="w-full bg-gray-50 dark:bg-gray-800/50 rounded-xl p-6 shadow-sm"
                                initial={{ opacity: 0, scale: 0.95 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5, delay: 1.6 }}
                            >
                                <p className="font-semibold text-gray-900 dark:text-gray-100 text-center mb-4">Weekly Rewards</p>
                                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-sm">
                                    <motion.div
                                        className="flex items-center gap-2 hover:text-amber-500 transition-colors duration-200"
                                        whileHover={{ x: 5 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
                                        0 coins for 50%
                                    </motion.div>
                                    <motion.div
                                        className="flex items-center gap-2 hover:text-amber-500 transition-colors duration-200"
                                        whileHover={{ x: 5 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
                                        2 coins for 60%
                                    </motion.div>
                                    <motion.div
                                        className="flex items-center gap-2 hover:text-amber-500 transition-colors duration-200"
                                        whileHover={{ x: 5 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
                                        4 coins for 70%
                                    </motion.div>
                                    <motion.div
                                        className="flex items-center gap-2 hover:text-amber-500 transition-colors duration-200"
                                        whileHover={{ x: 5 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
                                        6 coins for 80%
                                    </motion.div>
                                    <motion.div
                                        className="flex items-center gap-2 hover:text-amber-500 transition-colors duration-200"
                                        whileHover={{ x: 5 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
                                        8 coins for 90%
                                    </motion.div>
                                    <motion.div
                                        className="flex items-center gap-2 hover:text-amber-500 transition-colors duration-200"
                                        whileHover={{ x: 5 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
                                        10 coins for 100%
                                    </motion.div>
                                </div>
                                <p className="font-semibold text-center mt-4"><strong>Streak Bonus:</strong> +2 coins per weekly attempt</p>
                            </motion.div> */}
                        </div>
                    </Card>
                </motion.div>

                {/* Daily Quiz Card */}
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7, delay: 0.6 }}
                >
                    <Card className="max-w-4xl w-full mx-auto bg-white dark:bg-gray-800/90 border border-gray-200/50 dark:border-gray-700/50 rounded-3xl shadow-xl transition-all duration-500 p-8 backdrop-blur-sm">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div className="space-y-6 bg-gray-50 dark:bg-gray-800/50 rounded-xl p-6 shadow-sm transition-all duration-300">
                                <motion.div
                                    className="flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5, delay: 0.8 }}
                                    whileHover={{ scale: 1.02 }}
                                >
                                    <FaRegClipboard className="text-amber-500 text-2xl" />
                                    <span className="text-lg"><strong>Questions:</strong> 10</span>
                                </motion.div>
                                <motion.div
                                    className="flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5, delay: 1.0 }}
                                    whileHover={{ scale: 1.02 }}
                                >
                                    <GiMedal className="text-amber-500 text-2xl" />
                                    <span className="text-lg"><strong>Badges:</strong> Streak & Rank-Based</span>
                                </motion.div>
                                <motion.div
                                    className="flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5, delay: 1.1 }}
                                    whileHover={{ scale: 1.02 }}
                                >
                                    <FaClock className="text-amber-500 text-2xl" />
                                    <span className="text-lg"><strong>Duration:</strong> 8 min</span>
                                </motion.div>
                                <motion.div
                                    className="flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5, delay: 1.2 }}
                                    whileHover={{ scale: 1.02 }}
                                >
                                    <FaCoins className="text-amber-500 text-2xl" />
                                    <span className="text-lg"><strong>Earn Up to:</strong> 5 Coins</span>
                                </motion.div>
                                <motion.div
                                    className="flex items-center gap-4 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-colors duration-200"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5, delay: 1.2 }}
                                    whileHover={{ scale: 1.02 }}
                                >
                                    <BookCheck className="text-amber-500 text-2xl" />
                                    <span className="text-lg"><strong>Free Gift:</strong> Top 3 students get free classmate books</span>
                                </motion.div>
                            </div>
                            <div className="flex flex-col justify-between space-y-6">
                                <motion.div
                                    initial={{ opacity: 0, scale: 0.95 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.5, delay: 1.3 }}
                                >
                                    <MockExamButton />
                                </motion.div>
                                <p className="text-sm text-gray-600 dark:text-gray-300 text-center">
                                    Attempt once every 24 hours to earn coins & build your streak!
                                </p>
                                <motion.div
                                    className="text-sm bg-gray-100/50 dark:bg-gray-700/50 rounded-xl px-6 py-5 space-y-4 text-gray-800 dark:text-gray-200 backdrop-blur-sm shadow-sm"
                                    initial={{ opacity: 0, scale: 0.95 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.5, delay: 1.4 }}
                                >
                                    <p className="font-semibold text-gray-900 dark:text-gray-100">Coin Rewards:</p>
                                    <ul className="list-none space-y-2">
                                        <motion.li
                                            className="flex items-center gap-2 hover:text-amber-500 transition-colors duration-200"
                                            whileHover={{ x: 5 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
                                            0 coins for 50% score
                                        </motion.li>
                                        <motion.li
                                            className="flex items-center gap-2 hover:text-amber-500 transition-colors duration-200"
                                            whileHover={{ x: 5 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
                                            1 coins for 60% score
                                        </motion.li>
                                        <motion.li
                                            className="flex items-center gap-2 hover:text-amber-500 transition-colors duration-200"
                                            whileHover={{ x: 5 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
                                            2 coins for 70% score
                                        </motion.li>
                                        <motion.li
                                            className="flex items-center gap-2 hover:text-amber-500 transition-colors duration-200"
                                            whileHover={{ x: 5 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
                                            3 coins for 80% score
                                        </motion.li>
                                        <motion.li
                                            className="flex items-center gap-2 hover:text-amber-500 transition-colors duration-200"
                                            whileHover={{ x: 5 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
                                            4 coins for 90% score
                                        </motion.li>
                                        <motion.li
                                            className="flex items-center gap-2 hover:text-amber-500 transition-colors duration-200"
                                            whileHover={{ x: 5 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
                                            5 coins for 100% score
                                        </motion.li>
                                    </ul>
                                    <p className="font-semibold"><strong>Streak Bonus:</strong> +1 coin per daily attempt</p>
                                </motion.div>
                            </div>
                        </div>
                        <div className="flex flex-row flex-wrap gap-4 justify-center mt-8">
                            <div className="flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full">
                                <Image src="/scholer.svg" alt="100 Coins" width={48} height={48} />
                                <span className="text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis">
                                    100 Coins
                                </span>
                            </div>
                            <div className="flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full">
                                <Image src="/Mastermind.svg" alt="500 Coins" width={48} height={48} />
                                <span className="text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis">
                                    500 Coins
                                </span>
                            </div>
                            <div className="flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full">
                                <Image src="/Achiever.svg" alt="1000 Coins" width={48} height={48} />
                                <span className="text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis">
                                    1000 Coins
                                </span>
                            </div>
                            <div className="flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full">
                                <Image src="/Perfect Month.svg" alt="30 Days Streak" width={48} height={48} />
                                <span className="text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis">
                                    30 Days Streak
                                </span>
                            </div>
                            <div className="flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full">
                                <Image src="/Perfect Year.svg" alt="365 Days Streak" width={48} height={48} />
                                <span className="text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis">
                                    365 Days Streak
                                </span>
                            </div>
                            <div className="flex flex-col items-center bg-white p-3 rounded-xl shadow gap-2 w-auto max-w-full">
                                <Image src="/Streak.svg" alt="Daily Streak" width={48} height={48} />
                                <span className="text-sm font-semibold text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis">
                                    Daily Streak
                                </span>
                            </div>
                        </div>
                        <div className="mt-10 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: 1.4 }}
                            >
                                <Button
                                    variant="outline"
                                    className="w-full border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-customOrange hover:text-white transition-all duration-300 rounded-lg shadow-sm font-medium text-lg py-4"
                                    onClick={() => router.push(`/mock-exam-result/${studentId}`)}
                                >
                                    View Quiz Results & Earned Coins
                                </Button>
                            </motion.div>
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: 1.5 }}
                            >
                                <Button
                                    variant="outline"
                                    className="w-full border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-customOrange hover:text-white transition-all duration-300 rounded-lg shadow-sm font-medium text-lg py-4"
                                    onClick={() => router.push("/Leader-Board")}
                                >
                                    View Leaderboards
                                </Button>
                            </motion.div>
                        </div>
                    </Card>
                </motion.div>
            </section>

            <Footer />
        </div>
    );
};

export default Page;
