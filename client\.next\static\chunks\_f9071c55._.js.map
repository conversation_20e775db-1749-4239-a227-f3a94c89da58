{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('studentToken');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};\r\n\r\nexport const isAuthenticated = (): { isAuth: boolean; userType: 'STUDENT' | 'CLASS' | null } => {\r\n  const studentToken = getStudentAuthToken();\r\n  if (studentToken) {\r\n    return { isAuth: true, userType: 'STUDENT' };\r\n  }\r\n\r\n  if (typeof window !== 'undefined') {\r\n    const userData = localStorage.getItem('user');\r\n    if (userData) {\r\n      try {\r\n        const user = JSON.parse(userData);\r\n        if (user && user.id) {\r\n          return { isAuth: true, userType: 'CLASS' };\r\n        }\r\n      } catch (error) {\r\n        console.error('Error parsing user data:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  return { isAuth: false, userType: null };\r\n};"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,wCAAmC;QACjC,OAAO,aAAa,OAAO,CAAC;IAC9B;;AAEF;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX;AAEO,MAAM,kBAAkB;IAC7B,MAAM,eAAe;IACrB,IAAI,cAAc;QAChB,OAAO;YAAE,QAAQ;YAAM,UAAU;QAAU;IAC7C;IAEA,wCAAmC;QACjC,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,IAAI,UAAU;YACZ,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC;gBACxB,IAAI,QAAQ,KAAK,EAAE,EAAE;oBACnB,OAAO;wBAAE,QAAQ;wBAAM,UAAU;oBAAQ;gBAC3C;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF;IACF;IAEA,OAAO;QAAE,QAAQ;QAAO,UAAU;IAAK;AACzC", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentDetailServiceApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport const getStudentDetail = async (studentId: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(`/uwhizStudentData/${studentId}`);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed To Get Student Detail: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW;QACzE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,8BAA8B,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QAC1F;IACF;AACF", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/mock-exam-resultApi.ts"], "sourcesContent": ["import {axiosInstance} from \"../lib/axios\";\r\n\r\nexport const saveMockExamResult = async (data: any): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post(\"/mock-exam-result\", data, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam result: ${\r\n        error.response?.data?.message || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMockExamResults = async (\r\n  studentId: string,\r\n  page: number = 1,\r\n  limit: number = 10,\r\n  filter: { isWeekly?: boolean } = {} \r\n): Promise<any> => {\r\n  try {\r\n    const query = new URLSearchParams({\r\n      page: page.toString(),\r\n      limit: limit.toString(),\r\n      ...(filter.isWeekly !== undefined && { isWeekly: filter.isWeekly.toString() }),\r\n    }).toString();\r\n    const response = await axiosInstance.get(`/mock-exam-result/${studentId}?${query}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam result: ${\r\n        error.response?.data?.message || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,qBAAqB,MAAM;YACnE,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QACJ;IACF;AACF;AAEO,MAAM,qBAAqB,OAChC,WACA,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAAiC,CAAC,CAAC;IAEnC,IAAI;QACF,MAAM,QAAQ,IAAI,gBAAgB;YAChC,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;YACrB,GAAI,OAAO,QAAQ,KAAK,aAAa;gBAAE,UAAU,OAAO,QAAQ,CAAC,QAAQ;YAAG,CAAC;QAC/E,GAAG,QAAQ;QACX,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU,CAAC,EAAE,OAAO,EAAE;YAClF,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/uwhizMockExamTerminationApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport const saveTerminatedStudent = async (data: any) => {\r\n  try {\r\n    const response = await axiosInstance.post('/mock-exam-terminate', data,{\r\n       headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save termination log: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const countUwhizAttemp = async (studentId: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(`mock-exam-terminate/count?studentId=${studentId}`,{\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed To Get Count of termination: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,wBAAwB,OAAO;IAC1C,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,wBAAwB,MAAK;YACpE,SAAS;gBACR,iBAAiB;YACnB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QAC5F;IACF;AACF;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,WAAW,EAAC;YAC1F,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,oCAAoC,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QAChG;IACF;AACF", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/public/uwhizExam.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 798, height: 626, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAYAAAD+Bd/7AAAAjUlEQVR42lXMyw6CMBCF4UYot1qgBROwCiQiYY/s6vs/1i+wMLqZycz5coQQAhWH3EzG3WYMlTr2/tszsY88kRSppC1SXJlSqfi4v0AGJ6LwRLOBi46xKvoHQRDgnOPRXQ9Un38akiTBGMM0Pen7jrY2NDantiVSbmgYetb1hfce//bM88w4jizLgtaaD9PlOXjFshcbAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 6 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,4GAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAkS,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/uwhizMockExamApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport const uwhizMockQuestionForStudent = async (studentId:string,medium:string,isWeekly: boolean): Promise<any[]> => {\r\n    try {\r\n    const response = await axiosInstance.get(\r\n      `mock-exam/${studentId}/${medium}`,\r\n      {\r\n        params: { isWeekly: isWeekly.toString() }, \r\n        headers: {\r\n          'Server-Select': 'uwhizServer',\r\n        },\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(\r\n      `Failed To Get Question: ${error.response?.data?.message || error.message}`\r\n    );\r\n  }\r\n};"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,8BAA8B,OAAO,WAAiB,QAAc;IAC7E,IAAI;QACJ,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CACtC,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,QAAQ,EAClC;YACE,QAAQ;gBAAE,UAAU,SAAS,QAAQ;YAAG;YACxC,SAAS;gBACP,iBAAiB;YACnB;QACF;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MACR,CAAC,wBAAwB,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;IAE/E;AACF", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/mock-test/restictExamAttempt.tsx"], "sourcesContent": ["export const saveExamAttempt = (studentId: string) => {\r\n  const timestamp = Date.now();\r\n  const examAttempts = localStorage.getItem(\"examAttempts\");\r\n  const attempts: { [key: string]: number } = examAttempts ? JSON.parse(examAttempts) : {};\r\n  attempts[studentId] = timestamp;\r\n  localStorage.setItem(\"examAttempts\", JSON.stringify(attempts));\r\n};\r\n\r\nexport const isAttemptAllowed = (studentId: string): { allowed: boolean; remainingHours: number | null } => {\r\n  const examAttempts = localStorage.getItem(\"examAttempts\");\r\n  if (!examAttempts) {\r\n    return { allowed: true, remainingHours: null };\r\n  }\r\n\r\n  const attempts: { [key: string]: number } = JSON.parse(examAttempts);\r\n  const lastAttempt = attempts[studentId];\r\n\r\n  if (!lastAttempt) {\r\n    return { allowed: true, remainingHours: null };\r\n  }\r\n\r\n  const lastAttemptTime = lastAttempt;\r\n  const currentTime = Date.now();\r\n  const hours24InMs = 24 * 60 * 60 * 1000;\r\n  const timeSinceLastAttempt = currentTime - lastAttemptTime;\r\n\r\n  if (timeSinceLastAttempt >= hours24InMs) {\r\n    return { allowed: true, remainingHours: null };\r\n  }\r\n\r\n  const remainingMs = hours24InMs - timeSinceLastAttempt;\r\n  const remainingHours = Math.ceil(remainingMs / (60 * 60 * 1000));\r\n  return { allowed: false, remainingHours };\r\n};"], "names": [], "mappings": ";;;;AAAO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,eAAe,aAAa,OAAO,CAAC;IAC1C,MAAM,WAAsC,eAAe,KAAK,KAAK,CAAC,gBAAgB,CAAC;IACvF,QAAQ,CAAC,UAAU,GAAG;IACtB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;AACtD;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,eAAe,aAAa,OAAO,CAAC;IAC1C,IAAI,CAAC,cAAc;QACjB,OAAO;YAAE,SAAS;YAAM,gBAAgB;QAAK;IAC/C;IAEA,MAAM,WAAsC,KAAK,KAAK,CAAC;IACvD,MAAM,cAAc,QAAQ,CAAC,UAAU;IAEvC,IAAI,CAAC,aAAa;QAChB,OAAO;YAAE,SAAS;YAAM,gBAAgB;QAAK;IAC/C;IAEA,MAAM,kBAAkB;IACxB,MAAM,cAAc,KAAK,GAAG;IAC5B,MAAM,cAAc,KAAK,KAAK,KAAK;IACnC,MAAM,uBAAuB,cAAc;IAE3C,IAAI,wBAAwB,aAAa;QACvC,OAAO;YAAE,SAAS;YAAM,gBAAgB;QAAK;IAC/C;IAEA,MAAM,cAAc,cAAc;IAClC,MAAM,iBAAiB,KAAK,IAAI,CAAC,cAAc,CAAC,KAAK,KAAK,IAAI;IAC9D,OAAO;QAAE,SAAS;QAAO;IAAe;AAC1C", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/uestCoinTransctionApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport const addUestCoinTranscation = async (\r\n  data: any\r\n): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post(\"/uwhizCoinTransction/add\", data);\r\n    return { success: true, data: response.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to log transction of coins in mock exam: ${\r\n        error.response?.data?.message || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const updateUestCoins = async (data: {\r\n  modelId: string;\r\n  modelType: string;\r\n  coins: number;\r\n}): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post(\"/uwhizCoinTransction/update\", data);\r\n    return { success: true, data: response.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to update coins: ${\r\n        error.response?.data?.message || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,yBAAyB,OACpC;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,4BAA4B;QACtE,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gDAAgD,EACtD,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QACJ;IACF;AACF;AAEO,MAAM,kBAAkB,OAAO;IAKpC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,+BAA+B;QACzE,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,wBAAwB,EAC9B,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/mockExamStreakApi.ts"], "sourcesContent": ["import { axiosInstance } from \"../lib/axios\";\r\n\r\nexport const saveMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/mock-exam-streak/${studentId}`, {}, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-streak/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data }; \r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\nexport const saveweeklyMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/mock-exam-weekly-streak/${studentId}`, {}, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getweeklyMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-weekly-streak/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data }; \r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE,CAAC,GAAG;YAC7E,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE;YACzE,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AACO,MAAM,2BAA2B,OAAO;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,WAAW,EAAE,CAAC,GAAG;YACpF,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,WAAW,EAAE;YAChF,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/hooks/getStudentId.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { useSearchParams, useRouter } from 'next/navigation';\r\nimport { axiosInstance } from '@/lib/axios';\r\n\r\nconst useStudentId = () => {\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n  const [studentId, setStudentId] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const init = async () => {\r\n      const token = searchParams.get('token');\r\n\r\n      if (token) {\r\n        try {\r\n          const response = await axiosInstance.get(`/student/login-with-jwt`, {\r\n            params: { token },\r\n            withCredentials: true,\r\n          });\r\n\r\n          const data = response.data.data;\r\n\r\n         if (data?.userId) {\r\n            const studentData = {\r\n              id: data.userId,\r\n              contactNo: data.contactNo,\r\n              firstName: data.firstName,\r\n              lastName: data.lastName,\r\n            };\r\n\r\n            localStorage.setItem('student_data', JSON.stringify(studentData));\r\n            localStorage.setItem('studentToken', token);\r\n            localStorage.setItem('mobile_request', \"true\");\r\n\r\n            setStudentId(data.userId);\r\n          }\r\n\r\n          const newUrl = window.location.pathname;\r\n          router.replace(newUrl);\r\n        } catch (error) {\r\n          console.error('JWT login failed:', error);\r\n        }\r\n      } else {\r\n        const local = localStorage.getItem('student_data');\r\n        const parsed = local ? JSON.parse(local) : null;\r\n        setStudentId(parsed?.id || null);\r\n      }\r\n    };\r\n\r\n    init();\r\n  }, [searchParams, router]);\r\n\r\n  return studentId;\r\n};\r\n\r\nexport default useStudentId;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAMA,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;+CAAO;oBACX,MAAM,QAAQ,aAAa,GAAG,CAAC;oBAE/B,IAAI,OAAO;wBACT,IAAI;4BACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,uBAAuB,CAAC,EAAE;gCAClE,QAAQ;oCAAE;gCAAM;gCAChB,iBAAiB;4BACnB;4BAEA,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI;4BAEhC,IAAI,MAAM,QAAQ;gCACf,MAAM,cAAc;oCAClB,IAAI,KAAK,MAAM;oCACf,WAAW,KAAK,SAAS;oCACzB,WAAW,KAAK,SAAS;oCACzB,UAAU,KAAK,QAAQ;gCACzB;gCAEA,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gCACpD,aAAa,OAAO,CAAC,gBAAgB;gCACrC,aAAa,OAAO,CAAC,kBAAkB;gCAEvC,aAAa,KAAK,MAAM;4BAC1B;4BAEA,MAAM,SAAS,OAAO,QAAQ,CAAC,QAAQ;4BACvC,OAAO,OAAO,CAAC;wBACjB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,qBAAqB;wBACrC;oBACF,OAAO;wBACL,MAAM,QAAQ,aAAa,OAAO,CAAC;wBACnC,MAAM,SAAS,QAAQ,KAAK,KAAK,CAAC,SAAS;wBAC3C,aAAa,QAAQ,MAAM;oBAC7B;gBACF;;YAEA;QACF;iCAAG;QAAC;QAAc;KAAO;IAEzB,OAAO;AACT;GAjDM;;QACiB,qIAAA,CAAA,kBAAe;QACrB,qIAAA,CAAA,YAAS;;;uCAiDX", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/mock-test/mockExam.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport React, { useEffect, useState, useCallback, useMemo, useRef } from \"react\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport { getStudentDetail } from \"@/services/studentDetailServiceApi\";\r\nimport { saveMockExamResult, getMockExamResults } from \"@/services/mock-exam-resultApi\";\r\nimport { saveTerminatedStudent as saveTermination, countUwhizAttemp } from \"@/services/uwhizMockExamTerminationApi\";\r\nimport { Loader, Clock } from \"lucide-react\";\r\nimport { toast } from \"sonner\";\r\nimport Image from \"next/image\";\r\nimport examLogo from '../../../public/uwhizExam.png';\r\nimport { uwhizMockQuestionForStudent } from \"@/services/uwhizMockExamApi\";\r\nimport { saveExamAttempt } from \"./restictExamAttempt\";\r\nimport { addUestCoinTranscation, updateUestCoins } from \"@/services/uestCoinTransctionApi\";\r\nimport { saveMockExamStreak, getMockExamStreak, getweeklyMockExamStreak, saveweeklyMockExamStreak} from \"@/services/mockExamStreakApi\";\r\nimport useStudentId from \"@/hooks/getStudentId\";\r\nimport { WhatsappShareButton, WhatsappIcon } from 'react-share';\r\nimport * as htmlToImage from 'html-to-image';\r\n\r\ninterface Question {\r\n  id: string;\r\n  question: string;\r\n  optionOne: string;\r\n  optionTwo: string;\r\n  optionThree: string;\r\n  optionFour: string;\r\n  correctAnswer: string;\r\n}\r\n\r\ninterface UserAnswer {\r\n  questionId: string;\r\n  selectedAnswer: string;\r\n  isCorrect: boolean;\r\n}\r\n\r\ninterface StreakData {\r\n  streak: number;\r\n  lastAttempt: string;\r\n}\r\n\r\nconst QuizHeader = React.memo(\r\n  () => {\r\n    return (\r\n      <header className=\"fixed top-0 left-0 right-0 z-20 py-2 px-4 sm:px-6 sm:py-3 flex flex-col sm:flex-row items-center justify-between bg-black text-white shadow-md\">\r\n        <div className=\"flex items-center justify-center gap-3\">\r\n          <Image\r\n            height={60}\r\n            width={60}\r\n            src={examLogo.src}\r\n            alt=\"Uwhiz Logo\"\r\n            quality={100}\r\n            className=\"object-contain sm:h-20 sm:w-20\"\r\n          />\r\n          <h1 className=\"text-lg sm:text-2xl font-bold tracking-tight\">Uest Daily Quiz</h1>\r\n        </div>\r\n      </header>\r\n    );\r\n  }\r\n);\r\nQuizHeader.displayName = \"QuizHeader\";\r\n\r\nexport default function QuizPage() {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams(); \r\n  const studentId = useStudentId();\r\n  const [isWeekly, setIsWeekly] = useState(false); \r\n\r\n  const [isLoginDialogOpen, setIsLoginDialogOpen] = useState(false);\r\n  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState(false);\r\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\r\n  const [isExamTakenDialogOpen, setIsExamTakenDialogOpen] = useState(false);\r\n  const [showWarning, setShowWarning] = useState(false);\r\n  const [showTermination, setShowTermination] = useState(false);\r\n  const [questions, setQuestions] = useState<Question[]>([]);\r\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\r\n  const [timeLeft, setTimeLeft] = useState<number>(0);\r\n  const [userAnswers, setUserAnswers] = useState<UserAnswer[]>([]);\r\n  const [isQuizCompleted, setIsQuizCompleted] = useState(false);\r\n  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);\r\n  const [isApiCallPending, setIsApiCallPending] = useState(false);\r\n  const tickSoundRef = useRef<HTMLAudioElement | null>(null);\r\n  const [terminationMessage, setTerminationMessage] = useState<string>(\"\");\r\n  const [violationCount, setViolationCount] = useState(0);\r\n  const [showAnswerFeedback, setShowAnswerFeedback] = useState(false);\r\n  const [streakData, setStreakData] = useState<StreakData | null>(null);\r\n  const [streakLoading, setStreakLoading] = useState(false);\r\n  const [streakError, setStreakError] = useState<string | null>(null);\r\n  const cardRef = useRef<HTMLDivElement | null>(null);\r\n  const [isProfileNotApproved, setIsProfileNotApproved] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const isWeeklyParam = searchParams.get(\"isWeekly\");\r\n    setIsWeekly(isWeeklyParam === \"true\");\r\n  }, [searchParams]);\r\n\r\n  const initializeViolationCounts = async () => {\r\n    if (!studentId) {\r\n      return 0;\r\n    }\r\n    try {\r\n      const count = await countUwhizAttemp(studentId);\r\n      return typeof count === 'number' ? count : 0;\r\n    } catch (error) {\r\n      console.error(\"Failed to fetch violation count:\", error);\r\n      return 0;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const fetchCounts = async () => {\r\n      const count = await initializeViolationCounts();\r\n      setViolationCount(count);\r\n    };\r\n    fetchCounts();\r\n  }, [studentId]);\r\n\r\n  useEffect(() => {\r\n    if (violationCount >= 3) {\r\n      setShowTermination(true);\r\n      setTerminationMessage(\"Quiz terminated due to multiple cheating attempts.\");\r\n      if (studentId) {\r\n        saveExamAttempt(studentId);\r\n      }\r\n    }\r\n  }, [violationCount, studentId]);\r\n\r\n  useEffect(() => {\r\n    if (studentId) {\r\n      const checkExamEligibility = async () => {\r\n        try {\r\n          const response = await getMockExamResults(studentId, 1, 1, { isWeekly });\r\n          if (response.success && response.data.data.mockExamResults.length > 0) {\r\n            const latestExam = response.data.data.mockExamResults[0];\r\n            const examDate = new Date(latestExam.createdAt).toISOString().split('T')[0];\r\n            const today = new Date().toISOString().split('T')[0];\r\n\r\n            if (examDate === today && !isWeekly) {\r\n              setIsExamTakenDialogOpen(true);\r\n            } else if (isWeekly) {\r\n              const startOfWeek = new Date();\r\n              startOfWeek.setDate(startOfWeek.getDate() - (startOfWeek.getDay() === 0 ? 6 : startOfWeek.getDay() - 1));\r\n              startOfWeek.setHours(0, 0, 0, 0);\r\n              const hasAttemptedThisWeek = new Date(latestExam.createdAt) >= startOfWeek;\r\n              if (hasAttemptedThisWeek) {\r\n                setIsExamTakenDialogOpen(true);\r\n              }\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          toast.error(\"Failed to verify exam eligibility.\", error);\r\n        }\r\n      };\r\n      checkExamEligibility();\r\n    }\r\n  }, [studentId, isWeekly]);\r\n\r\n  useEffect(() => {\r\n    tickSoundRef.current = new Audio(\"/clock-ticking-sound-effect.mp3\");\r\n    tickSoundRef.current.loop = true;\r\n    return () => {\r\n      if (tickSoundRef.current) {\r\n        tickSoundRef.current.pause();\r\n        tickSoundRef.current = null;\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (\r\n      questions.length > 0 &&\r\n      timeLeft <= 5 &&\r\n      timeLeft > 0 &&\r\n      !isDialogOpen &&\r\n      !showTermination &&\r\n      !isQuizCompleted &&\r\n      !isLoginDialogOpen &&\r\n      !isProfileDialogOpen &&\r\n      !isExamTakenDialogOpen &&\r\n      tickSoundRef.current\r\n    ) {\r\n      tickSoundRef.current.play().catch((error) => {\r\n        console.error(\"Failed to play tick sound:\", error);\r\n      });\r\n    } else if (tickSoundRef.current) {\r\n      tickSoundRef.current.pause();\r\n    }\r\n  }, [timeLeft, questions, isDialogOpen, showTermination, isQuizCompleted, isLoginDialogOpen, isProfileDialogOpen, isExamTakenDialogOpen]);\r\n\r\n  const handleNextQuestion = useCallback(async () => {\r\n    const currentQuestion = questions[currentQuestionIndex];\r\n    if (selectedAnswer) {\r\n      const isCorrect = selectedAnswer === currentQuestion.correctAnswer;\r\n      setUserAnswers((prev) => [\r\n        ...prev,\r\n        {\r\n          questionId: currentQuestion.id,\r\n          selectedAnswer,\r\n          isCorrect,\r\n        },\r\n      ]);\r\n      setShowAnswerFeedback(true);\r\n      setTimeout(() => {\r\n        setShowAnswerFeedback(false);\r\n        setSelectedAnswer(null);\r\n        if (currentQuestionIndex < questions.length - 1) {\r\n          setCurrentQuestionIndex((prev) => prev + 1);\r\n        } else {\r\n          setIsQuizCompleted(true);\r\n        }\r\n      }, 1000);\r\n    } else {\r\n      setUserAnswers((prev) => [\r\n        ...prev,\r\n        {\r\n          questionId: currentQuestion.id,\r\n          selectedAnswer: \"skipped\",\r\n          isCorrect: false,\r\n        },\r\n      ]);\r\n      toast.warning(\"Question skipped.\");\r\n      if (currentQuestionIndex < questions.length - 1) {\r\n        setCurrentQuestionIndex((prev) => prev + 1);\r\n      } else {\r\n        setIsQuizCompleted(true);\r\n      }\r\n    }\r\n  }, [selectedAnswer, questions, currentQuestionIndex]);\r\n\r\n  const calculateScore = useMemo(() => {\r\n    return userAnswers.reduce((score, answer) => score + (answer.isCorrect ? 1 : 0), 0);\r\n  }, [userAnswers]);\r\n\r\n  const calculateCoins = useMemo(() => {\r\n    const percentage = (calculateScore / questions.length) * 100;\r\n    let baseCoins;\r\n    if (percentage >= 100) baseCoins = 5;\r\n    else if (percentage >= 90) baseCoins = 4;\r\n    else if (percentage >= 80) baseCoins = 3;\r\n    else if (percentage >= 70) baseCoins = 2;\r\n    else if (percentage >= 60) baseCoins = 1;\r\n    else baseCoins = 0;\r\n    return isWeekly ? baseCoins * 5 : baseCoins;\r\n  }, [calculateScore, questions.length, isWeekly]);\r\n\r\n  useEffect(() => {\r\n    if (isQuizCompleted && studentId) {\r\n      const saveResultAndCoins = async () => {\r\n        try {\r\n          const streakResponse = isWeekly\r\n            ? await saveweeklyMockExamStreak(studentId)\r\n            : await saveMockExamStreak(studentId);\r\n          if (!streakResponse.success) {\r\n            toast.error(streakResponse.error);\r\n            setStreakError(streakResponse.error);\r\n            return;\r\n          }\r\n          setStreakLoading(true);\r\n          const streakFetchResponse = isWeekly\r\n            ? await getweeklyMockExamStreak(studentId)\r\n            : await getMockExamStreak(studentId);\r\n          setStreakLoading(false);\r\n          if (streakFetchResponse.success) {\r\n            setStreakData(streakFetchResponse.data);\r\n            toast.success(`Streak updated successfully! Current streak: ${streakFetchResponse.data.streak} ${isWeekly ? 'Weeks' : 'Days'}`);\r\n          } else {\r\n            setStreakError(streakFetchResponse.error);\r\n            toast.error(streakFetchResponse.error);\r\n          }\r\n\r\n          const streakCoins = 1; \r\n          const totalCoins = calculateCoins + streakCoins;\r\n\r\n          const resultData = {\r\n            studentId,\r\n            score: calculateScore,\r\n            coinEarnings: totalCoins,\r\n            isWeekly,\r\n          };\r\n          const response = await saveMockExamResult(resultData);\r\n          if (response.success) {\r\n            toast.success(\"Result saved successfully!\");\r\n          } else {\r\n            toast.error(response.error);\r\n          }\r\n\r\n          const coinsData = {\r\n            modelId: studentId,\r\n            modelType: \"STUDENT\",\r\n            coins: totalCoins,\r\n          };\r\n          const updateCoinsResponse = await updateUestCoins(coinsData);\r\n          if (updateCoinsResponse.success) {\r\n            toast.success(`Coins updated successfully! Added ${totalCoins} coins (Score: ${calculateCoins}, Streak: ${streakCoins}).`);\r\n          } else {\r\n            toast.error(updateCoinsResponse.error);\r\n          }\r\n\r\n          const transactionData = {\r\n            modelId: studentId,\r\n            modelType: \"STUDENT\",\r\n            amount: totalCoins,\r\n            type: \"CREDIT\",\r\n            reason: `${isWeekly ? 'Weekly' : 'Daily'} Quiz Exam (Score + Streak: ${streakCoins})`,\r\n          };\r\n          const addTransactionResponse = await addUestCoinTranscation(transactionData);\r\n          if (addTransactionResponse.success) {\r\n            toast.success(\"Transaction logged successfully!\");\r\n          } else {\r\n            toast.error(addTransactionResponse.error);\r\n          }\r\n\r\n          await saveExamAttempt(studentId);\r\n          await exitFullScreen();\r\n        } catch (error: any) {\r\n          setStreakLoading(false);\r\n          setStreakError(`Failed to fetch streak: ${error.message}`);\r\n          toast.error(`Failed to save result, update coins, or update streak: ${error.message}`);\r\n        }\r\n      };\r\n      saveResultAndCoins();\r\n    }\r\n  }, [isQuizCompleted, studentId, calculateScore, calculateCoins, isWeekly]);\r\n\r\n  useEffect(() => {\r\n    if (questions.length > 0 && timeLeft > 0 && !isDialogOpen && !showTermination && !isLoginDialogOpen && !isProfileDialogOpen && !isExamTakenDialogOpen) {\r\n      const timer = setInterval(() => {\r\n        setTimeLeft((prev) => {\r\n          const newTime = prev - 1;\r\n          if (newTime <= 0) {\r\n            clearInterval(timer);\r\n            handleNextQuestion();\r\n            return 0;\r\n          }\r\n          return newTime;\r\n        });\r\n      }, 1000);\r\n      return () => clearInterval(timer);\r\n    }\r\n  }, [timeLeft, questions, currentQuestionIndex, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, isExamTakenDialogOpen, handleNextQuestion]);\r\n\r\n  useEffect(() => {\r\n    if (questions.length > 0 && !isDialogOpen && !showTermination && !isLoginDialogOpen && !isProfileDialogOpen && !isExamTakenDialogOpen) {\r\n      const newTime = 45;\r\n      setTimeLeft(newTime);\r\n    }\r\n  }, [currentQuestionIndex, questions, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, isExamTakenDialogOpen]);\r\n\r\n  useEffect(() => {\r\n    if (isQuizCompleted && typeof window !== 'undefined') {\r\n      import('canvas-confetti').then((module) => {\r\n        const confetti = module.default;\r\n        confetti({\r\n          particleCount: 100,\r\n          spread: 80,\r\n          startVelocity: 30,\r\n          ticks: 200,\r\n          origin: { x: 0.5, y: 0.2 },\r\n          colors: ['#FF4500', '#FFD700', '#FF69B4', '#00FF00', '#1E90FF'],\r\n          shapes: ['square'],\r\n          gravity: 0.3,\r\n          scalar: 1.2,\r\n        });\r\n      });\r\n    }\r\n  }, [isQuizCompleted]);\r\n\r\n  const fetchQuizState = useCallback(async () => {\r\n    if (!studentId) {\r\n      setIsLoginDialogOpen(true);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const studentResponse = await getStudentDetail(studentId);\r\n      if (!studentResponse.success) {\r\n        toast.error(studentResponse.error);\r\n        setIsProfileDialogOpen(true);\r\n        return;\r\n      }\r\n      if (studentResponse.data.status != \"APPROVED\") {\r\n        setIsProfileDialogOpen(true);\r\n        setIsProfileNotApproved(true);\r\n        toast.error(\"Student Profile is not approved yet. Please check your profile.\");\r\n        return;\r\n      }\r\n      const medium = studentResponse.data.medium.toUpperCase();\r\n\r\n      const questionResponse = await uwhizMockQuestionForStudent(studentId, medium,isWeekly);\r\n      if (questionResponse && Array.isArray(questionResponse)) {\r\n        setQuestions(questionResponse);\r\n        setTimeLeft(45);\r\n        setIsDialogOpen(true);\r\n      } else {\r\n        toast.error(\"No questions found or invalid response.\");\r\n      }\r\n    } catch (error: any) {\r\n      toast.error(error);\r\n    }\r\n  }, [studentId, isWeekly]);\r\n\r\n  useEffect(() => {\r\n    if (studentId && !isExamTakenDialogOpen) {\r\n      fetchQuizState();\r\n    }\r\n  }, [studentId, fetchQuizState, isExamTakenDialogOpen]);\r\n\r\n  const exitFullScreen = async (maxAttempts = 3, attempt = 1): Promise<boolean> => {\r\n    try {\r\n      if (document.fullscreenElement || (document as any).webkitFullscreenElement || (document as any).mozFullScreenElement) {\r\n        if (document.exitFullscreen) {\r\n          await document.exitFullscreen();\r\n        } else if ((document as any).webkitExitFullscreen) {\r\n          await (document as any).webkitExitFullscreen();\r\n        } else if ((document as any).mozCancelFullScreen) {\r\n          await (document as any).mozCancelFullScreen();\r\n        }\r\n        await new Promise(resolve => setTimeout(resolve, 100));\r\n        if (!document.fullscreenElement && !(document as any).webkitFullscreenElement && !(document as any).mozFullScreenElement) {\r\n          return true;\r\n        }\r\n        if (attempt < maxAttempts) {\r\n          return await exitFullScreen(maxAttempts, attempt + 1);\r\n        }\r\n        throw new Error(\"Max attempts reached\");\r\n      }\r\n      return true;\r\n    } catch (err: unknown) {\r\n      console.error(`Failed to exit full-screen mode (attempt ${attempt}):`, err);\r\n      if (attempt < maxAttempts) {\r\n        await new Promise(resolve => setTimeout(resolve, 500));\r\n        return await exitFullScreen(maxAttempts, attempt + 1);\r\n      }\r\n      toast.error(\"Failed to exit full-screen mode. Please press Esc to exit manually.\");\r\n      return false;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isQuizCompleted && studentId && showTermination) {\r\n      saveExamAttempt(studentId);\r\n      exitFullScreen();\r\n    }\r\n  }, [isQuizCompleted, studentId]);\r\n\r\n  useEffect(() => {\r\n    if (showTermination && studentId) {\r\n      exitFullScreen().then((success) => {\r\n        console.log(\"Quiz terminated and full-screen exited.\", success);\r\n      });\r\n    }\r\n  }, [showTermination, studentId]);\r\n\r\n  const enterFullScreen = () => {\r\n    const element = document.documentElement;\r\n    if (element.requestFullscreen) {\r\n      element.requestFullscreen().catch((err: unknown) => console.error(\"Failed to enter fullscreen:\", err));\r\n    }\r\n  };\r\n\r\n  const handleStartQuiz = () => {\r\n    setIsDialogOpen(false);\r\n    enterFullScreen();\r\n    if (questions.length > 0) {\r\n      setTimeLeft(45);\r\n    }\r\n  };\r\n\r\n  const handleGoToProfile = () => {\r\n    router.push(\"/student/profile\");\r\n  };\r\n\r\n  const handleCloseExamTakenDialog = () => {\r\n    setIsExamTakenDialogOpen(false);\r\n    router.push(\"/mock-exam-card\");\r\n  };\r\n\r\n  const handleKeyDown = useCallback(\r\n    async (event: KeyboardEvent) => {\r\n      if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;\r\n\r\n      const restrictedKeys = [\"Alt\", \"Control\", \"Tab\", \"Shift\", \"Enter\"];\r\n      const functionKeys = [\"F1\", \"F2\", \"F3\", \"F4\", \"F5\", \"F6\", \"F7\", \"F8\", \"F9\", \"F10\", \"F11\", \"F12\"];\r\n      const isDevToolsShortcut =\r\n        (event.ctrlKey && event.shiftKey && (event.key === \"I\" || event.key === \"J\" || event.key === \"C\")) ||\r\n        (event.metaKey && event.altKey && event.key === \"I\") ||\r\n        event.key === \"F12\";\r\n      const isCopyShortcut =\r\n        (event.ctrlKey || event.metaKey) && (event.key === \"c\" || event.key === \"C\");\r\n\r\n      if (\r\n        restrictedKeys.includes(event.key) ||\r\n        functionKeys.includes(event.key) ||\r\n        isDevToolsShortcut ||\r\n        isCopyShortcut\r\n      ) {\r\n        event.preventDefault();\r\n        if (isCopyShortcut) {\r\n          toast.warning(\"Copying is disabled during the quiz.\");\r\n          return;\r\n        }\r\n        if (!studentId) {\r\n          setViolationCount(0);\r\n          return;\r\n        }\r\n        setIsApiCallPending(true);\r\n        try {\r\n          const violationType = isDevToolsShortcut\r\n            ? \"DevTools shortcut\"\r\n            : functionKeys.includes(event.key)\r\n              ? `Function key \"${event.key}\"`\r\n              : `Restricted key \"${event.key}\"`;\r\n          await saveTermination({\r\n            studentId,\r\n            reason: violationType,\r\n          });\r\n          const updatedCount = await countUwhizAttemp(studentId);\r\n          setViolationCount(updatedCount);\r\n          if (updatedCount === 1) {\r\n            setShowWarning(true);\r\n            toast.warning(`${violationType} detected.`);\r\n          } else if (updatedCount === 2) {\r\n            setShowWarning(true);\r\n            toast.warning(`${violationType} detected. One more violation will terminate the quiz.`);\r\n          } else if (updatedCount >= 3) {\r\n            setShowTermination(true);\r\n            setTerminationMessage(\"Quiz terminated due to multiple cheating attempts.\");\r\n            if (studentId) {\r\n              saveExamAttempt(studentId);\r\n            }\r\n            toast.error(\"Quiz terminated due to multiple cheating attempts.\");\r\n          }\r\n        } catch (error: unknown) {\r\n          toast.error(\"Failed to save termination record.\", { description: error instanceof Error ? error.message : \"Unknown error\" });\r\n        } finally {\r\n          setIsApiCallPending(false);\r\n        }\r\n      }\r\n    },\r\n    [studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]\r\n  );\r\n\r\n  const handleVisibilityChange = useCallback(async () => {\r\n    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;\r\n\r\n    if (document.hidden) {\r\n      setIsApiCallPending(true);\r\n      try {\r\n        await saveTermination({\r\n          studentId,\r\n          reason: \"Tab switch\",\r\n        });\r\n        if (!studentId) {\r\n          setViolationCount(0);\r\n          return;\r\n        }\r\n        const updatedCount = await countUwhizAttemp(studentId as string);\r\n        setViolationCount(updatedCount);\r\n        if (updatedCount === 1) {\r\n          setShowWarning(true);\r\n          toast.warning(\"Tab switch detected.\");\r\n        } else if (updatedCount === 2) {\r\n          setShowWarning(true);\r\n          toast.warning(\"Again tab switch detected. One more violation will terminate the quiz.\");\r\n        } else if (updatedCount >= 3) {\r\n          setShowTermination(true);\r\n          setTerminationMessage(\"Quiz terminated due to multiple cheating attempts.\");\r\n          if (studentId) {\r\n            saveExamAttempt(studentId);\r\n          }\r\n          toast.error(\"Quiz terminated due to multiple cheating attempts.\");\r\n        }\r\n      } catch (error: unknown) {\r\n        toast.error(\"Failed to save termination record.\", { description: error instanceof Error ? error.message : \"Unknown error\" });\r\n      } finally {\r\n        setIsApiCallPending(false);\r\n      }\r\n    }\r\n  }, [studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]);\r\n\r\n  const handleContextMenu = useCallback(\r\n    async (event: MouseEvent) => {\r\n      if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;\r\n      event.preventDefault();\r\n      toast.warning(\"Right-click is disabled during the quiz.\");\r\n    },\r\n    [studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]\r\n  );\r\n\r\n  const handleWindowBlur = useCallback(async () => {\r\n    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;\r\n\r\n    setIsApiCallPending(true);\r\n    try {\r\n      await saveTermination({\r\n        studentId,\r\n        reason: \"Window blur\",\r\n      });\r\n      const updatedCount = await countUwhizAttemp(studentId as string);\r\n      setViolationCount(updatedCount);\r\n      if (updatedCount === 1) {\r\n        setShowWarning(true);\r\n        toast.warning(\"Window focus lost.\");\r\n      } else if (updatedCount == 2) {\r\n        setShowWarning(true);\r\n        toast.warning(\"Window focus lost again. One more violation will terminate the quiz.\");\r\n      } else if (updatedCount >= 3) {\r\n        setShowTermination(true);\r\n        setTerminationMessage(\"Quiz terminated due to multiple cheating attempts.\");\r\n        if (studentId) {\r\n          saveExamAttempt(studentId);\r\n        }\r\n        toast.error(\"Quiz terminated due to multiple cheating attempts.\");\r\n      }\r\n    } catch (error: unknown) {\r\n      toast.error(\"Failed to save termination record.\", { description: error instanceof Error ? error.message : \"Unknown error\" });\r\n    } finally {\r\n      setIsApiCallPending(false);\r\n    }\r\n  }, [studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]);\r\n\r\n  const handleFullScreenChange = useCallback(async () => {\r\n    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;\r\n\r\n    if (!document.fullscreenElement) {\r\n      setIsApiCallPending(true);\r\n      try {\r\n        await saveTermination({\r\n          studentId,\r\n          reason: \"Full-screen exit\",\r\n        });\r\n        if (!studentId) {\r\n          setViolationCount(0);\r\n          return;\r\n        }\r\n        const updatedCount = await countUwhizAttemp(studentId as string);\r\n        setViolationCount(updatedCount);\r\n        if (updatedCount === 1) {\r\n          setShowWarning(true);\r\n          toast.warning(\"You have exited full-screen mode.\");\r\n        } else if (updatedCount === 2) {\r\n          setShowWarning(true);\r\n          toast.warning(\"Again you have exited full-screen mode. One more violation will terminate the quiz.\");\r\n        } else if (updatedCount >= 3) {\r\n          setShowTermination(true);\r\n          setTerminationMessage(\"Quiz terminated due to multiple cheating attempts.\");\r\n          if (studentId) {\r\n            saveExamAttempt(studentId);\r\n          }\r\n          toast.error(\"Quiz terminated due to multiple cheating attempts.\");\r\n        }\r\n      } catch (error: unknown) {\r\n        toast.error(\"Failed to save termination record.\", { description: error instanceof Error ? error.message : \"Unknown error\" });\r\n      } finally {\r\n        setIsApiCallPending(false);\r\n      }\r\n    }\r\n  }, [studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]);\r\n\r\n  const handleGoHome = async () => {\r\n    setShowTermination(false);\r\n\r\n    const mobileRequest = localStorage.getItem('mobile_request');\r\n\r\n    if (mobileRequest === 'true') {\r\n      localStorage.removeItem('mobile_request');\r\n      window.location.href = 'UEST://DailyQuiz';\r\n    }\r\n\r\n    const isFullScreen =\r\n      document.fullscreenElement ||\r\n      (document as any).webkitFullscreenElement ||\r\n      (document as any).mozFullScreenElement;\r\n\r\n    if (isFullScreen) {\r\n      const success = await exitFullScreen();\r\n      if (success) {\r\n        router.push(\"/mock-exam-card\");\r\n      } else {\r\n        toast.warning(\"Could not exit full-screen mode automatically. Please press Esc to exit manually.\");\r\n        router.push(\"/mock-exam-card\");\r\n      }\r\n    } else {\r\n      router.push(\"/mock-exam-card\");\r\n    }\r\n\r\n    setTimeout(() => {\r\n      router.push(\"/mock-exam-card\");\r\n    }, 1000);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!isDialogOpen && !isLoginDialogOpen && !isProfileDialogOpen && !showTermination && !isExamTakenDialogOpen) {\r\n      document.addEventListener(\"visibilitychange\", handleVisibilityChange);\r\n      document.addEventListener(\"keydown\", handleKeyDown);\r\n      window.addEventListener(\"blur\", handleWindowBlur);\r\n      document.addEventListener(\"contextmenu\", handleContextMenu);\r\n      document.addEventListener(\"fullscreenchange\", handleFullScreenChange);\r\n    }\r\n    return () => {\r\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\r\n      document.removeEventListener(\"keydown\", handleKeyDown);\r\n      window.removeEventListener(\"blur\", handleWindowBlur);\r\n      document.removeEventListener(\"contextmenu\", handleContextMenu);\r\n      document.removeEventListener(\"fullscreenchange\", handleFullScreenChange);\r\n    };\r\n  }, [\r\n    handleVisibilityChange,\r\n    handleKeyDown,\r\n    handleWindowBlur,\r\n    handleContextMenu,\r\n    handleFullScreenChange,\r\n    isDialogOpen,\r\n    isLoginDialogOpen,\r\n    isProfileDialogOpen,\r\n    showTermination,\r\n    isExamTakenDialogOpen,\r\n  ]);\r\n\r\n  const formatTime = useCallback((seconds: number) => {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return `${minutes.toString().padStart(2, \"0\")}:${secs.toString().padStart(2, \"0\")}`;\r\n  }, []);\r\n\r\n  const progress = useMemo(() => {\r\n    return questions.length > 0\r\n      ? ((currentQuestionIndex + 1) / questions.length) * 100\r\n      : 0;\r\n  }, [currentQuestionIndex, questions]);\r\n\r\n  const getButtonClass = (optionKey: string) => {\r\n    const baseClass = `w-full h-auto min-h-[60px] sm:min-h-[80px] whitespace-normal text-wrap font-medium rounded-lg py-3 sm:py-4 text-sm sm:text-lg text-gray-700 hover:bg-orange-100 hover:border-orange-500 transition-all duration-200 flex items-start justify-start gap-3 px-3 sm:px-6 shadow-sm border border-gray-200 bg-white`;\r\n\r\n    if (selectedAnswer === optionKey) {\r\n      return `${baseClass} bg-orange-100 border-orange-500`;\r\n    }\r\n    return baseClass;\r\n  };\r\n\r\n  const handleOptionClick = (optionKey: string) => {\r\n    if (!showAnswerFeedback) {\r\n      setSelectedAnswer(optionKey);\r\n    }\r\n  };\r\n\r\n  if (isLoginDialogOpen) {\r\n    return (\r\n      <div className=\"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900\">\r\n        <div className=\"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96\">\r\n          <h2 className=\"text-lg sm:text-2xl font-bold mb-4\">Login Required</h2>\r\n          <p className=\"mb-4 text-sm sm:text-base text-gray-600\">\r\n            Please log in as a student to access the quiz.\r\n          </p>\r\n          <Button\r\n            onClick={() => router.push(`/student/login?redirect=/mock-test${isWeekly ? '?isWeekly=true' : ''}`)}\r\n            className=\"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all\"\r\n          >\r\n            Login to Continue\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isProfileDialogOpen) {\r\n    const heading = isProfileNotApproved\r\n      ? 'Profile Not Approved'\r\n      : 'Complete Your Profile';\r\n\r\n    const message = isProfileNotApproved\r\n      ? 'Your profile has not been approved yet. Please wait for approval and check notifications.'\r\n      : 'Your profile is incomplete. Please complete your profile to proceed.';\r\n\r\n    return (\r\n      <div className=\"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900\">\r\n        <div className=\"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96\">\r\n          <h2 className=\"text-lg sm:text-2xl font-bold mb-4\">{heading}</h2>\r\n          <p className=\"mb-4 text-sm sm:text-base text-gray-600\">{message}</p>\r\n          <Button\r\n            onClick={handleGoToProfile}\r\n            className=\"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all\"\r\n          >\r\n            {isProfileNotApproved ? 'Update Profile' : 'Complete Profile'}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isExamTakenDialogOpen) {\r\n    return (\r\n      <div className=\"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900\">\r\n        <div className=\"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96\">\r\n          <h2 className=\"text-lg sm:text-2xl font-bold mb-4 text-black\">Exam Already Taken</h2>\r\n          <p className=\"mb-4 text-sm sm:text-base text-gray-600\">\r\n            {isWeekly\r\n              ? \"You have already attempted the weekly exam this week. Please try again next Sunday.\"\r\n              : \"You have already attempted the daily exam today. Please try again tomorrow.\"}\r\n          </p>\r\n          <Button\r\n            onClick={handleCloseExamTakenDialog}\r\n            className=\"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all\"\r\n          >\r\n            Go to Home\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (questions.length === 0) {\r\n    return (\r\n      <div className=\"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900\">\r\n        <p className=\"text-base sm:text-xl font-medium mr-4\">Loading questions...</p>\r\n        <Loader className=\"w-5 h-5 sm:w-8 sm:h-8 animate-spin text-customOrange\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handleDownload = async () => {\r\n    if (cardRef.current) {\r\n      try {\r\n        const { width } = cardRef.current.getBoundingClientRect();\r\n        const scrollHeight = cardRef.current.scrollHeight;\r\n\r\n        const dataUrl = await htmlToImage.toJpeg(cardRef.current, {\r\n          quality: 1.0,\r\n          pixelRatio: 2,\r\n          backgroundColor: '#ffffff',\r\n          canvasWidth: width,\r\n          canvasHeight: scrollHeight,\r\n          style: {\r\n            margin: \"0\",\r\n            padding: \"35px 0px 0px 20px  \",\r\n          },\r\n        });\r\n\r\n        const link = document.createElement('a');\r\n        link.href = dataUrl;\r\n        link.download = isWeekly ? 'uest-weekly-quiz-result.jpg' : 'uest-daily-quiz-result.jpg';\r\n        link.click();\r\n      } catch (error) {\r\n        console.error('Failed to download card:', error);\r\n        toast.error('Failed to download the quiz result card. Please try again.');\r\n      }\r\n    }\r\n  };\r\n\r\n  if (isQuizCompleted) {\r\n    const shareUrl = \"https://uest.in/mock-exam-card\";\r\n    const shareText = `I scored ${calculateScore}/${questions.length} with a streak of ${streakData?.streak || 0} ${isWeekly ? 'weeks' : 'days'} on U-whiz ${isWeekly ? 'Weekly' : 'Daily'} Daily Exam! 🎉 Try it out!`;\r\n\r\n    const studentData = localStorage.getItem('student_data');\r\n    const parsedStudentData = studentData ? JSON.parse(studentData) : null;\r\n    const studentName = parsedStudentData ? `${parsedStudentData.firstName} ${parsedStudentData.lastName}` : 'Unknown Student';\r\n\r\n    return (\r\n      <div className=\"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200\">\r\n        <div className=\"relative z-10 w-full max-w-md text-center\">\r\n          <div ref={cardRef} className=\"bg-white dark:bg-[#1f2937] rounded-3xl shadow-2xl border border-orange-100 dark:border-gray-700 p-6 mx-auto max-w-md\">\r\n            <div className=\"mb-6 text-center text-sm text-gray-600 dark:text-gray-400 flex flex-col items-center\">\r\n              <p>Powered by</p>\r\n              <Image\r\n                src=\"/logo.png\"\r\n                alt=\"Preply Logo\"\r\n                width={120}\r\n                height={40}\r\n              />\r\n            </div>\r\n            <div className=\"absolute inset-0 z-0 pointer-events-none\">\r\n              <div className=\"animate-pulse opacity-20 bg-[radial-gradient(#facc15_1px,transparent_1px)] bg-[length:20px_20px] w-full h-full\" />\r\n            </div>\r\n            <h1 className=\"text-3xl font-extrabold text-customOrange dark:text-orange-400 mb-4 text-center\">{isWeekly ? 'Weekly' : 'Daily'} Quiz Completed 🎉</h1>\r\n            <p className=\"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2 text-center\">\r\n              Congratulations, <span className=\"text-customOrange font-bold\">{studentName}</span>\r\n            </p>\r\n            <p className=\"text-base text-gray-600 dark:text-gray-400 mb-4 text-center\">\r\n              Keep up the momentum. 💪\r\n            </p>\r\n            <div className=\"w-1/3 mx-auto h-2 bg-gray-200 dark:bg-gray-600 rounded-full mb-4\">\r\n              <div\r\n                className=\"h-full bg-customOrange dark:bg-orange-400 rounded-full transition-all duration-500\"\r\n                style={{ width: `${questions.length > 0 ? (calculateScore / questions.length) * 100 : 0}%` }}\r\n              ></div>\r\n            </div>\r\n            <p className=\"text-base text-gray-800 dark:text-gray-200 mb-4 text-center\">\r\n              Final Score: <span className=\"font-bold\">{calculateScore}</span> / {questions.length}\r\n            </p>\r\n            <div className=\"flex justify-center mb-4\">\r\n              <div className=\"relative flex flex-col items-center\">\r\n                <div className=\"absolute w-24 h-24 rounded-full bg-orange-500 opacity-30 blur-xl animate-ping\" />\r\n                <div className=\"absolute w-16 h-16 rounded-full bg-red-500 opacity-20 blur-md animate-pulse\" />\r\n                <div className=\"absolute w-12 h-12 rounded-full bg-yellow-300 opacity-40 blur-sm animate-bounce\" />\r\n                <div className=\"relative w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-red-500 flex items-center justify-center shadow-lg animate-burning\">\r\n                  <span className=\"text-4xl\">🔥</span>\r\n                </div>\r\n                <span className=\"mt-2 text-sm font-semibold text-gray-600 dark:text-gray-400 text-center\">\r\n                  {streakLoading ? 'Loading Streak...' : streakError ? 'Error Loading Streak' : `🔥 Streak: ${streakData?.streak || 0} ${isWeekly ? 'Weeks' : 'Days'}`}\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <div className=\"text-sm text-gray-800 dark:text-gray-300 space-y-1 mb-6 text-center\">\r\n              <p><strong>Coins Earned:</strong> {calculateCoins + (streakData?.streak || 0)}</p>\r\n              <p><strong>Bonus:</strong> +{streakData?.streak || 0} for Streak!</p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex flex-col mt-6 bg-white dark:bg-[#1f2937] rounded-3xl shadow-2xl border border-orange-100 dark:border-gray-700 p-6 mx-auto max-w-xl\">\r\n            <div className=\"flex flex-col items-center justify-between gap-4\">\r\n              <p className=\"text-sm text-gray-800 dark:text-gray-300 flex-1\">\r\n                Ask your friends to join the quiz and get a chance to win some coins!\r\n              </p>\r\n              <div className=\"social-media-buttons flex items-center gap-4\">\r\n                <WhatsappShareButton url={shareUrl} title={shareText}>\r\n                  <WhatsappIcon size={32} round />\r\n                </WhatsappShareButton>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex mt-4 gap-4\">\r\n              <button\r\n                onClick={handleDownload}\r\n                className=\"download-button bg-orange-500 text-white px-6 py-3 rounded-lg shadow-md hover:bg-orange-600 min-w-[150px] whitespace-nowrap\"\r\n              >\r\n                📷 Download Result\r\n              </button>\r\n              <button\r\n                onClick={handleGoHome}\r\n                className=\"bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold px-6 py-3 rounded-lg shadow-md min-w-[150px] whitespace-nowrap\"\r\n              >\r\n                🚀 Continue Learning\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const currentQuestion = questions[currentQuestionIndex];\r\n\r\n  return (\r\n    <div className=\"flex flex-col min-h-screen bg-gray-100 text-gray-900\">\r\n      {isDialogOpen && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-3/4 md:w-1/2 max-h-[80vh] flex flex-col overflow-y-auto\">\r\n            <h2 className=\"text-lg sm:text-2xl font-bold mb-4\">Start {isWeekly ? 'Weekly' : 'Daily'} Quiz</h2>\r\n            <p className=\"font-semibold mb-4 text-sm sm:text-base text-gray-600\">\r\n              Note: This is a mock exam for testing purposes only.\r\n            </p>\r\n            <div className=\"flex-1 overflow-y-auto pr-2 mb-4 text-sm sm:text-base\">\r\n              <p className=\"font-semibold mb-2\">Instructions (English):</p>\r\n              <ul className=\"list-disc list-inside mb-4 text-gray-600\">\r\n                <li>Do not switch tabs during the quiz.</li>\r\n                <li>Do not use restricted keys (Alt, Ctrl, Tab, Shift, Enter, Function keys).</li>\r\n                <li>Do not open Developer Tools.</li>\r\n                <li>Do not exit full-screen mode.</li>\r\n                <li>Do not interact with other windows or applications.</li>\r\n                <li>Do not change the screen or minimize the quiz window.</li>\r\n                <li>Do not receive or make calls during the quiz.</li>\r\n                <li>Do not use split screen or floating windows on your device.</li>\r\n              </ul>\r\n              <p className=\"font-semibold mb-2\">સૂચનાઓ (ગુજરાતી):</p>\r\n              <ul className=\"list-disc list-inside text-gray-600\">\r\n                <li>ક્વિઝ દરમિયાન ટેબ બદલશો નહીં.</li>\r\n                <li>પ્રતિબંધિત કીઓ (ઓલ્ટ, કંટ્રોલ, ટેબ, શિફ્ટ, એન્ટર, ફંક્શન કીઓ) નો ઉપયોગ કરશો નહીં.</li>\r\n                <li>ડેવલપર ટૂલ્સ ખોલશો નહીં.</li>\r\n                <li>ક્વિઝ દરમિયાન જમણું-ક્લિક કરશો નહીં.</li>\r\n                <li>ફુલ-સ્ક્રીન મોડમાંથી બહાર નીકળશો નહીં.</li>\r\n                <li>અન્ય વિન્ડોઝ અથવા એપ્લિકેશન્સ સાથે સંપર્ક કરશો નહીં.</li>\r\n                <li>સ્ક્રીન બદલશો નહીં અથવા ક્વિઝ વિન્ડો નાની કરશો નહીં.</li>\r\n                <li>ક્વિઝ દરમિયાન કૉલ રિસીવ કરશો નહીં અથવા કૉલ કરશો નહીં.</li>\r\n                <li>તમારા ડિવાઇસ પર સ્પ્લિટ સ્ક્રીન અથવા ફ્લોટિંગ વિન્ડોઝનો ઉપયોગ કરશો નહીં.</li>\r\n              </ul>\r\n            </div>\r\n            <Button\r\n              onClick={handleStartQuiz}\r\n              className=\"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all\"\r\n            >\r\n              Start {isWeekly ? 'Weekly' : 'Daily'} Quiz\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {showWarning && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96\">\r\n            <h2 className=\"text-lg sm:text-2xl font-bold mb-4 text-customOrange\">Warning</h2>\r\n            <p className=\"mb-4 text-sm sm:text-base text-gray-600\">\r\n              You have performed a restricted action. Repeating this will terminate the quiz.\r\n            </p>\r\n            <Button\r\n              onClick={() => setShowWarning(false)}\r\n              className=\"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all\"\r\n            >\r\n              OK\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {showTermination && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96\">\r\n            <h2 className=\"text-lg sm:text-2xl font-bold mb-4 text-red-500\">Quiz Terminated</h2>\r\n            <p className=\"mb-4 text-sm sm:text-base text-gray-600\">\r\n              {terminationMessage || \"Your quiz has been terminated due to multiple cheating attempts.\"}\r\n            </p>\r\n            <Button\r\n              onClick={handleGoHome}\r\n              className=\"bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 text-sm sm:text-base w-full transition-all\"\r\n            >\r\n              Go to Home\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {!isDialogOpen && !isLoginDialogOpen && !isProfileDialogOpen && !isExamTakenDialogOpen && (\r\n        <>\r\n          <QuizHeader />\r\n\r\n          <div className=\"fixed top-[60px] sm:top-[80px] left-0 right-0 z-10 w-full h-1.5 bg-gray-200\">\r\n            <div\r\n              className=\"h-3.5 bg-customOrange rounded-r-full transition-all duration-300\"\r\n              style={{ width: `${progress}%` }}\r\n            />\r\n          </div>\r\n          <div className=\"flex-1 flex flex-col items-center justify-center px-4 sm:px-6 pt-[80px] sm:pt-[100px] pb-[48px] sm:pb-[64px] min-h-screen\">\r\n            <div className=\"flex flex-col items-center justify-center w-full max-w-3xl\">\r\n              <div className=\"mt-2 sm:mt-4 mb-4 sm:mb-6 flex items-center gap-2 bg-gray-800/80 px-4 sm:px-6 py-2 rounded-full shadow-lg\">\r\n                <Clock className=\"w-5 h-5 sm:w-6 sm:h-6 text-customOrange animate-pulse\" />\r\n                <span className=\"text-lg sm:text-2xl font-bold text-customOrange\">\r\n                  {formatTime(timeLeft)}\r\n                </span>\r\n              </div>\r\n              <div className=\"w-full text-center flex flex-col items-center\">\r\n                <div className=\"flex justify-center mb-3 sm:mb-4\">\r\n                  <span className=\"text-xs sm:text-base font-semibold text-customOrange bg-orange-100 px-2 sm:px-3 py-1 rounded-full shadow-sm\">\r\n                    Question {currentQuestionIndex + 1} of {questions.length}\r\n                  </span>\r\n                </div>\r\n                <div className=\"bg-white p-4 sm:p-8 rounded-lg shadow-xl mb-6 w-full max-h-[60vh] sm:max-h-[70vh] overflow-y-auto\">\r\n                  <h2 className=\"text-lg sm:text-2xl md:text-3xl font-bold text-gray-800 mb-4 sm:mb-6\">\r\n                    {currentQuestion.question}\r\n                  </h2>\r\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 w-full\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className={getButtonClass(\"optionOne\")}\r\n                      onClick={() => handleOptionClick(\"optionOne\")}\r\n                      disabled={showTermination || showAnswerFeedback}\r\n                    >\r\n                      <span className=\"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0\">\r\n                        A\r\n                      </span>\r\n                      <span className=\"flex-1 text-left whitespace-normal break-words\">{currentQuestion.optionOne}</span>\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className={getButtonClass(\"optionTwo\")}\r\n                      onClick={() => handleOptionClick(\"optionTwo\")}\r\n                      disabled={showTermination || showAnswerFeedback}\r\n                    >\r\n                      <span className=\"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0\">\r\n                        B\r\n                      </span>\r\n                      <span className=\"flex-1 text-left whitespace-normal break-words\">{currentQuestion.optionTwo}</span>\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className={getButtonClass(\"optionThree\")}\r\n                      onClick={() => handleOptionClick(\"optionThree\")}\r\n                      disabled={showTermination || showAnswerFeedback}\r\n                    >\r\n                      <span className=\"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0\">\r\n                        C\r\n                      </span>\r\n                      <span className=\"flex-1 text-left whitespace-normal break-words\">{currentQuestion.optionThree}</span>\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className={getButtonClass(\"optionFour\")}\r\n                      onClick={() => handleOptionClick(\"optionFour\")}\r\n                      disabled={showTermination || showAnswerFeedback}\r\n                    >\r\n                      <span className=\"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0\">\r\n                        D\r\n                      </span>\r\n                      <span className=\"flex-1 text-left whitespace-normal break-words\">{currentQuestion.optionFour}</span>\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n                <Button\r\n                  className=\"bg-customOrange text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg font-semibold shadow-lg transform hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                  onClick={() => handleNextQuestion()}\r\n                  disabled={showTermination || showAnswerFeedback}\r\n                >\r\n                  {currentQuestionIndex === questions.length - 1 ? \"Finish\" : \"Next Question\"}\r\n                </Button>\r\n              </div>\r\n              <footer className=\"fixed bottom-0 left-0 right-0 bg-black text-white py-2 px-4 sm:px-6 flex items-center justify-center gap-1.5 sm:gap-2 text-xs sm:text-sm\">\r\n                <span>Powered by</span>\r\n                <span className=\"font-semibold\">UEST EdTech</span>\r\n              </footer>\r\n            </div>\r\n          </div>\r\n        </>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAlBA;;;;;;;;;;;;;;;;;;AAyCA,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,IAAI,CAC3B;IACE,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,gIAAA,CAAA,UAAK;oBACJ,QAAQ;oBACR,OAAO;oBACP,KAAK,yQAAA,CAAA,UAAQ,CAAC,GAAG;oBACjB,KAAI;oBACJ,SAAS;oBACT,WAAU;;;;;;8BAEZ,6LAAC;oBAAG,WAAU;8BAA+C;;;;;;;;;;;;;;;;;AAIrE;KAjBI;AAmBN,WAAW,WAAW,GAAG;AAEV,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,UAAY,AAAD;IAC7B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA2B;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAC9C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,gBAAgB,aAAa,GAAG,CAAC;YACvC,YAAY,kBAAkB;QAChC;6BAAG;QAAC;KAAa;IAEjB,MAAM,4BAA4B;QAChC,IAAI,CAAC,WAAW;YACd,OAAO;QACT;QACA,IAAI;YACF,MAAM,QAAQ,MAAM,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD,EAAE;YACrC,OAAO,OAAO,UAAU,WAAW,QAAQ;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;kDAAc;oBAClB,MAAM,QAAQ,MAAM;oBACpB,kBAAkB;gBACpB;;YACA;QACF;6BAAG;QAAC;KAAU;IAEd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,kBAAkB,GAAG;gBACvB,mBAAmB;gBACnB,sBAAsB;gBACtB,IAAI,WAAW;oBACb,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE;gBAClB;YACF;QACF;6BAAG;QAAC;QAAgB;KAAU;IAE9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,WAAW;gBACb,MAAM;+DAAuB;wBAC3B,IAAI;4BACF,MAAM,WAAW,MAAM,CAAA,GAAA,+IAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,GAAG,GAAG;gCAAE;4BAAS;4BACtE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,GAAG;gCACrE,MAAM,aAAa,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;gCACxD,MAAM,WAAW,IAAI,KAAK,WAAW,SAAS,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gCAC3E,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gCAEpD,IAAI,aAAa,SAAS,CAAC,UAAU;oCACnC,yBAAyB;gCAC3B,OAAO,IAAI,UAAU;oCACnB,MAAM,cAAc,IAAI;oCACxB,YAAY,OAAO,CAAC,YAAY,OAAO,KAAK,CAAC,YAAY,MAAM,OAAO,IAAI,IAAI,YAAY,MAAM,KAAK,CAAC;oCACtG,YAAY,QAAQ,CAAC,GAAG,GAAG,GAAG;oCAC9B,MAAM,uBAAuB,IAAI,KAAK,WAAW,SAAS,KAAK;oCAC/D,IAAI,sBAAsB;wCACxB,yBAAyB;oCAC3B;gCACF;4BACF;wBACF,EAAE,OAAO,OAAY;4BACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sCAAsC;wBACpD;oBACF;;gBACA;YACF;QACF;6BAAG;QAAC;QAAW;KAAS;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,aAAa,OAAO,GAAG,IAAI,MAAM;YACjC,aAAa,OAAO,CAAC,IAAI,GAAG;YAC5B;sCAAO;oBACL,IAAI,aAAa,OAAO,EAAE;wBACxB,aAAa,OAAO,CAAC,KAAK;wBAC1B,aAAa,OAAO,GAAG;oBACzB;gBACF;;QACF;6BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IACE,UAAU,MAAM,GAAG,KACnB,YAAY,KACZ,WAAW,KACX,CAAC,gBACD,CAAC,mBACD,CAAC,mBACD,CAAC,qBACD,CAAC,uBACD,CAAC,yBACD,aAAa,OAAO,EACpB;gBACA,aAAa,OAAO,CAAC,IAAI,GAAG,KAAK;0CAAC,CAAC;wBACjC,QAAQ,KAAK,CAAC,8BAA8B;oBAC9C;;YACF,OAAO,IAAI,aAAa,OAAO,EAAE;gBAC/B,aAAa,OAAO,CAAC,KAAK;YAC5B;QACF;6BAAG;QAAC;QAAU;QAAW;QAAc;QAAiB;QAAiB;QAAmB;QAAqB;KAAsB;IAEvI,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YACrC,MAAM,kBAAkB,SAAS,CAAC,qBAAqB;YACvD,IAAI,gBAAgB;gBAClB,MAAM,YAAY,mBAAmB,gBAAgB,aAAa;gBAClE;gEAAe,CAAC,OAAS;+BACpB;4BACH;gCACE,YAAY,gBAAgB,EAAE;gCAC9B;gCACA;4BACF;yBACD;;gBACD,sBAAsB;gBACtB;gEAAW;wBACT,sBAAsB;wBACtB,kBAAkB;wBAClB,IAAI,uBAAuB,UAAU,MAAM,GAAG,GAAG;4BAC/C;4EAAwB,CAAC,OAAS,OAAO;;wBAC3C,OAAO;4BACL,mBAAmB;wBACrB;oBACF;+DAAG;YACL,OAAO;gBACL;gEAAe,CAAC,OAAS;+BACpB;4BACH;gCACE,YAAY,gBAAgB,EAAE;gCAC9B,gBAAgB;gCAChB,WAAW;4BACb;yBACD;;gBACD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,IAAI,uBAAuB,UAAU,MAAM,GAAG,GAAG;oBAC/C;oEAAwB,CAAC,OAAS,OAAO;;gBAC3C,OAAO;oBACL,mBAAmB;gBACrB;YACF;QACF;mDAAG;QAAC;QAAgB;QAAW;KAAqB;IAEpD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YAC7B,OAAO,YAAY,MAAM;oDAAC,CAAC,OAAO,SAAW,QAAQ,CAAC,OAAO,SAAS,GAAG,IAAI,CAAC;mDAAG;QACnF;2CAAG;QAAC;KAAY;IAEhB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YAC7B,MAAM,aAAa,AAAC,iBAAiB,UAAU,MAAM,GAAI;YACzD,IAAI;YACJ,IAAI,cAAc,KAAK,YAAY;iBAC9B,IAAI,cAAc,IAAI,YAAY;iBAClC,IAAI,cAAc,IAAI,YAAY;iBAClC,IAAI,cAAc,IAAI,YAAY;iBAClC,IAAI,cAAc,IAAI,YAAY;iBAClC,YAAY;YACjB,OAAO,WAAW,YAAY,IAAI;QACpC;2CAAG;QAAC;QAAgB,UAAU,MAAM;QAAE;KAAS;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,mBAAmB,WAAW;gBAChC,MAAM;6DAAqB;wBACzB,IAAI;4BACF,MAAM,iBAAiB,WACnB,MAAM,CAAA,GAAA,uIAAA,CAAA,2BAAwB,AAAD,EAAE,aAC/B,MAAM,CAAA,GAAA,uIAAA,CAAA,qBAAkB,AAAD,EAAE;4BAC7B,IAAI,CAAC,eAAe,OAAO,EAAE;gCAC3B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,eAAe,KAAK;gCAChC,eAAe,eAAe,KAAK;gCACnC;4BACF;4BACA,iBAAiB;4BACjB,MAAM,sBAAsB,WACxB,MAAM,CAAA,GAAA,uIAAA,CAAA,0BAAuB,AAAD,EAAE,aAC9B,MAAM,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EAAE;4BAC5B,iBAAiB;4BACjB,IAAI,oBAAoB,OAAO,EAAE;gCAC/B,cAAc,oBAAoB,IAAI;gCACtC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,6CAA6C,EAAE,oBAAoB,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,UAAU,QAAQ;4BAChI,OAAO;gCACL,eAAe,oBAAoB,KAAK;gCACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,oBAAoB,KAAK;4BACvC;4BAEA,MAAM,cAAc;4BACpB,MAAM,aAAa,iBAAiB;4BAEpC,MAAM,aAAa;gCACjB;gCACA,OAAO;gCACP,cAAc;gCACd;4BACF;4BACA,MAAM,WAAW,MAAM,CAAA,GAAA,+IAAA,CAAA,qBAAkB,AAAD,EAAE;4BAC1C,IAAI,SAAS,OAAO,EAAE;gCACpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4BAChB,OAAO;gCACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,KAAK;4BAC5B;4BAEA,MAAM,YAAY;gCAChB,SAAS;gCACT,WAAW;gCACX,OAAO;4BACT;4BACA,MAAM,sBAAsB,MAAM,CAAA,GAAA,2IAAA,CAAA,kBAAe,AAAD,EAAE;4BAClD,IAAI,oBAAoB,OAAO,EAAE;gCAC/B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,kCAAkC,EAAE,WAAW,eAAe,EAAE,eAAe,UAAU,EAAE,YAAY,EAAE,CAAC;4BAC3H,OAAO;gCACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,oBAAoB,KAAK;4BACvC;4BAEA,MAAM,kBAAkB;gCACtB,SAAS;gCACT,WAAW;gCACX,QAAQ;gCACR,MAAM;gCACN,QAAQ,GAAG,WAAW,WAAW,QAAQ,4BAA4B,EAAE,YAAY,CAAC,CAAC;4BACvF;4BACA,MAAM,yBAAyB,MAAM,CAAA,GAAA,2IAAA,CAAA,yBAAsB,AAAD,EAAE;4BAC5D,IAAI,uBAAuB,OAAO,EAAE;gCAClC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4BAChB,OAAO;gCACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,uBAAuB,KAAK;4BAC1C;4BAEA,MAAM,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE;4BACtB,MAAM;wBACR,EAAE,OAAO,OAAY;4BACnB,iBAAiB;4BACjB,eAAe,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;4BACzD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uDAAuD,EAAE,MAAM,OAAO,EAAE;wBACvF;oBACF;;gBACA;YACF;QACF;6BAAG;QAAC;QAAiB;QAAW;QAAgB;QAAgB;KAAS;IAEzE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,UAAU,MAAM,GAAG,KAAK,WAAW,KAAK,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,uBAAuB;gBACrJ,MAAM,QAAQ;gDAAY;wBACxB;wDAAY,CAAC;gCACX,MAAM,UAAU,OAAO;gCACvB,IAAI,WAAW,GAAG;oCAChB,cAAc;oCACd;oCACA,OAAO;gCACT;gCACA,OAAO;4BACT;;oBACF;+CAAG;gBACH;0CAAO,IAAM,cAAc;;YAC7B;QACF;6BAAG;QAAC;QAAU;QAAW;QAAsB;QAAc;QAAiB;QAAmB;QAAqB;QAAuB;KAAmB;IAEhK,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,UAAU,MAAM,GAAG,KAAK,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,uBAAuB;gBACrI,MAAM,UAAU;gBAChB,YAAY;YACd;QACF;6BAAG;QAAC;QAAsB;QAAW;QAAc;QAAiB;QAAmB;QAAqB;KAAsB;IAElI,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,mBAAmB,aAAkB,aAAa;gBACpD,4JAA0B,IAAI;0CAAC,CAAC;wBAC9B,MAAM,WAAW,OAAO,OAAO;wBAC/B,SAAS;4BACP,eAAe;4BACf,QAAQ;4BACR,eAAe;4BACf,OAAO;4BACP,QAAQ;gCAAE,GAAG;gCAAK,GAAG;4BAAI;4BACzB,QAAQ;gCAAC;gCAAW;gCAAW;gCAAW;gCAAW;6BAAU;4BAC/D,QAAQ;gCAAC;6BAAS;4BAClB,SAAS;4BACT,QAAQ;wBACV;oBACF;;YACF;QACF;6BAAG;QAAC;KAAgB;IAEpB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YACjC,IAAI,CAAC,WAAW;gBACd,qBAAqB;gBACrB;YACF;YAEA,IAAI;gBACF,MAAM,kBAAkB,MAAM,CAAA,GAAA,6IAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC/C,IAAI,CAAC,gBAAgB,OAAO,EAAE;oBAC5B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB,KAAK;oBACjC,uBAAuB;oBACvB;gBACF;gBACA,IAAI,gBAAgB,IAAI,CAAC,MAAM,IAAI,YAAY;oBAC7C,uBAAuB;oBACvB,wBAAwB;oBACxB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ;gBACF;gBACA,MAAM,SAAS,gBAAgB,IAAI,CAAC,MAAM,CAAC,WAAW;gBAEtD,MAAM,mBAAmB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD,EAAE,WAAW,QAAO;gBAC7E,IAAI,oBAAoB,MAAM,OAAO,CAAC,mBAAmB;oBACvD,aAAa;oBACb,YAAY;oBACZ,gBAAgB;gBAClB,OAAO;oBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF,EAAE,OAAO,OAAY;gBACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;+CAAG;QAAC;QAAW;KAAS;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,aAAa,CAAC,uBAAuB;gBACvC;YACF;QACF;6BAAG;QAAC;QAAW;QAAgB;KAAsB;IAErD,MAAM,iBAAiB,OAAO,cAAc,CAAC,EAAE,UAAU,CAAC;QACxD,IAAI;YACF,IAAI,SAAS,iBAAiB,IAAI,AAAC,SAAiB,uBAAuB,IAAI,AAAC,SAAiB,oBAAoB,EAAE;gBACrH,IAAI,SAAS,cAAc,EAAE;oBAC3B,MAAM,SAAS,cAAc;gBAC/B,OAAO,IAAI,AAAC,SAAiB,oBAAoB,EAAE;oBACjD,MAAM,AAAC,SAAiB,oBAAoB;gBAC9C,OAAO,IAAI,AAAC,SAAiB,mBAAmB,EAAE;oBAChD,MAAM,AAAC,SAAiB,mBAAmB;gBAC7C;gBACA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,IAAI,CAAC,SAAS,iBAAiB,IAAI,CAAC,AAAC,SAAiB,uBAAuB,IAAI,CAAC,AAAC,SAAiB,oBAAoB,EAAE;oBACxH,OAAO;gBACT;gBACA,IAAI,UAAU,aAAa;oBACzB,OAAO,MAAM,eAAe,aAAa,UAAU;gBACrD;gBACA,MAAM,IAAI,MAAM;YAClB;YACA,OAAO;QACT,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,QAAQ,EAAE,CAAC,EAAE;YACvE,IAAI,UAAU,aAAa;gBACzB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,OAAO,MAAM,eAAe,aAAa,UAAU;YACrD;YACA,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,mBAAmB,aAAa,iBAAiB;gBACnD,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE;gBAChB;YACF;QACF;6BAAG;QAAC;QAAiB;KAAU;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,mBAAmB,WAAW;gBAChC,iBAAiB,IAAI;0CAAC,CAAC;wBACrB,QAAQ,GAAG,CAAC,2CAA2C;oBACzD;;YACF;QACF;6BAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,kBAAkB;QACtB,MAAM,UAAU,SAAS,eAAe;QACxC,IAAI,QAAQ,iBAAiB,EAAE;YAC7B,QAAQ,iBAAiB,GAAG,KAAK,CAAC,CAAC,MAAiB,QAAQ,KAAK,CAAC,+BAA+B;QACnG;IACF;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;QAChB;QACA,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,YAAY;QACd;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,6BAA6B;QACjC,yBAAyB;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAC9B,OAAO;YACL,IAAI,gBAAgB,qBAAqB,uBAAuB,eAAe,oBAAoB,uBAAuB;YAE1H,MAAM,iBAAiB;gBAAC;gBAAO;gBAAW;gBAAO;gBAAS;aAAQ;YAClE,MAAM,eAAe;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAO;gBAAO;aAAM;YAChG,MAAM,qBACJ,AAAC,MAAM,OAAO,IAAI,MAAM,QAAQ,IAAI,CAAC,MAAM,GAAG,KAAK,OAAO,MAAM,GAAG,KAAK,OAAO,MAAM,GAAG,KAAK,GAAG,KAC/F,MAAM,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,GAAG,KAAK,OAChD,MAAM,GAAG,KAAK;YAChB,MAAM,iBACJ,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,KAAK,CAAC,MAAM,GAAG,KAAK,OAAO,MAAM,GAAG,KAAK,GAAG;YAE7E,IACE,eAAe,QAAQ,CAAC,MAAM,GAAG,KACjC,aAAa,QAAQ,CAAC,MAAM,GAAG,KAC/B,sBACA,gBACA;gBACA,MAAM,cAAc;gBACpB,IAAI,gBAAgB;oBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd;gBACF;gBACA,IAAI,CAAC,WAAW;oBACd,kBAAkB;oBAClB;gBACF;gBACA,oBAAoB;gBACpB,IAAI;oBACF,MAAM,gBAAgB,qBAClB,sBACA,aAAa,QAAQ,CAAC,MAAM,GAAG,IAC7B,CAAC,cAAc,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,GAC7B,CAAC,gBAAgB,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;oBACrC,MAAM,CAAA,GAAA,iJAAA,CAAA,wBAAe,AAAD,EAAE;wBACpB;wBACA,QAAQ;oBACV;oBACA,MAAM,eAAe,MAAM,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC5C,kBAAkB;oBAClB,IAAI,iBAAiB,GAAG;wBACtB,eAAe;wBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,cAAc,UAAU,CAAC;oBAC5C,OAAO,IAAI,iBAAiB,GAAG;wBAC7B,eAAe;wBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,cAAc,sDAAsD,CAAC;oBACxF,OAAO,IAAI,gBAAgB,GAAG;wBAC5B,mBAAmB;wBACnB,sBAAsB;wBACtB,IAAI,WAAW;4BACb,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE;wBAClB;wBACA,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd;gBACF,EAAE,OAAO,OAAgB;oBACvB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sCAAsC;wBAAE,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAAgB;gBAC5H,SAAU;oBACR,oBAAoB;gBACtB;YACF;QACF;8CACA;QAAC;QAAW;QAAc;QAAmB;QAAqB;QAAa;QAAkB;KAAsB;IAGzH,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YACzC,IAAI,gBAAgB,qBAAqB,uBAAuB,eAAe,oBAAoB,uBAAuB;YAE1H,IAAI,SAAS,MAAM,EAAE;gBACnB,oBAAoB;gBACpB,IAAI;oBACF,MAAM,CAAA,GAAA,iJAAA,CAAA,wBAAe,AAAD,EAAE;wBACpB;wBACA,QAAQ;oBACV;oBACA,IAAI,CAAC,WAAW;wBACd,kBAAkB;wBAClB;oBACF;oBACA,MAAM,eAAe,MAAM,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC5C,kBAAkB;oBAClB,IAAI,iBAAiB,GAAG;wBACtB,eAAe;wBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO,IAAI,iBAAiB,GAAG;wBAC7B,eAAe;wBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO,IAAI,gBAAgB,GAAG;wBAC5B,mBAAmB;wBACnB,sBAAsB;wBACtB,IAAI,WAAW;4BACb,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE;wBAClB;wBACA,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd;gBACF,EAAE,OAAO,OAAgB;oBACvB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sCAAsC;wBAAE,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAAgB;gBAC5H,SAAU;oBACR,oBAAoB;gBACtB;YACF;QACF;uDAAG;QAAC;QAAW;QAAc;QAAmB;QAAqB;QAAa;QAAkB;KAAsB;IAE1H,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAClC,OAAO;YACL,IAAI,gBAAgB,qBAAqB,uBAAuB,eAAe,oBAAoB,uBAAuB;YAC1H,MAAM,cAAc;YACpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;kDACA;QAAC;QAAW;QAAc;QAAmB;QAAqB;QAAa;QAAkB;KAAsB;IAGzH,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YACnC,IAAI,gBAAgB,qBAAqB,uBAAuB,eAAe,oBAAoB,uBAAuB;YAE1H,oBAAoB;YACpB,IAAI;gBACF,MAAM,CAAA,GAAA,iJAAA,CAAA,wBAAe,AAAD,EAAE;oBACpB;oBACA,QAAQ;gBACV;gBACA,MAAM,eAAe,MAAM,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC5C,kBAAkB;gBAClB,IAAI,iBAAiB,GAAG;oBACtB,eAAe;oBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB,OAAO,IAAI,gBAAgB,GAAG;oBAC5B,eAAe;oBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB,OAAO,IAAI,gBAAgB,GAAG;oBAC5B,mBAAmB;oBACnB,sBAAsB;oBACtB,IAAI,WAAW;wBACb,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE;oBAClB;oBACA,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF,EAAE,OAAO,OAAgB;gBACvB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sCAAsC;oBAAE,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAAgB;YAC5H,SAAU;gBACR,oBAAoB;YACtB;QACF;iDAAG;QAAC;QAAW;QAAc;QAAmB;QAAqB;QAAa;QAAkB;KAAsB;IAE1H,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YACzC,IAAI,gBAAgB,qBAAqB,uBAAuB,eAAe,oBAAoB,uBAAuB;YAE1H,IAAI,CAAC,SAAS,iBAAiB,EAAE;gBAC/B,oBAAoB;gBACpB,IAAI;oBACF,MAAM,CAAA,GAAA,iJAAA,CAAA,wBAAe,AAAD,EAAE;wBACpB;wBACA,QAAQ;oBACV;oBACA,IAAI,CAAC,WAAW;wBACd,kBAAkB;wBAClB;oBACF;oBACA,MAAM,eAAe,MAAM,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC5C,kBAAkB;oBAClB,IAAI,iBAAiB,GAAG;wBACtB,eAAe;wBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO,IAAI,iBAAiB,GAAG;wBAC7B,eAAe;wBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO,IAAI,gBAAgB,GAAG;wBAC5B,mBAAmB;wBACnB,sBAAsB;wBACtB,IAAI,WAAW;4BACb,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE;wBAClB;wBACA,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd;gBACF,EAAE,OAAO,OAAgB;oBACvB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sCAAsC;wBAAE,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAAgB;gBAC5H,SAAU;oBACR,oBAAoB;gBACtB;YACF;QACF;uDAAG;QAAC;QAAW;QAAc;QAAmB;QAAqB;QAAa;QAAkB;KAAsB;IAE1H,MAAM,eAAe;QACnB,mBAAmB;QAEnB,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAE3C,IAAI,kBAAkB,QAAQ;YAC5B,aAAa,UAAU,CAAC;YACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QAEA,MAAM,eACJ,SAAS,iBAAiB,IAC1B,AAAC,SAAiB,uBAAuB,IACzC,AAAC,SAAiB,oBAAoB;QAExC,IAAI,cAAc;YAChB,MAAM,UAAU,MAAM;YACtB,IAAI,SAAS;gBACX,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd;QACF,OAAO;YACL,OAAO,IAAI,CAAC;QACd;QAEA,WAAW;YACT,OAAO,IAAI,CAAC;QACd,GAAG;IACL;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,uBAAuB;gBAC7G,SAAS,gBAAgB,CAAC,oBAAoB;gBAC9C,SAAS,gBAAgB,CAAC,WAAW;gBACrC,OAAO,gBAAgB,CAAC,QAAQ;gBAChC,SAAS,gBAAgB,CAAC,eAAe;gBACzC,SAAS,gBAAgB,CAAC,oBAAoB;YAChD;YACA;sCAAO;oBACL,SAAS,mBAAmB,CAAC,oBAAoB;oBACjD,SAAS,mBAAmB,CAAC,WAAW;oBACxC,OAAO,mBAAmB,CAAC,QAAQ;oBACnC,SAAS,mBAAmB,CAAC,eAAe;oBAC5C,SAAS,mBAAmB,CAAC,oBAAoB;gBACnD;;QACF;6BAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE,CAAC;YAC9B,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;YACrC,MAAM,OAAO,UAAU;YACvB,OAAO,GAAG,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QACrF;2CAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sCAAE;YACvB,OAAO,UAAU,MAAM,GAAG,IACtB,AAAC,CAAC,uBAAuB,CAAC,IAAI,UAAU,MAAM,GAAI,MAClD;QACN;qCAAG;QAAC;QAAsB;KAAU;IAEpC,MAAM,iBAAiB,CAAC;QACtB,MAAM,YAAY,CAAC,+SAA+S,CAAC;QAEnU,IAAI,mBAAmB,WAAW;YAChC,OAAO,GAAG,UAAU,gCAAgC,CAAC;QACvD;QACA,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,oBAAoB;YACvB,kBAAkB;QACpB;IACF;IAEA,IAAI,mBAAmB;QACrB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,6LAAC;wBAAE,WAAU;kCAA0C;;;;;;kCAGvD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,kCAAkC,EAAE,WAAW,mBAAmB,IAAI;wBAClG,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,qBAAqB;QACvB,MAAM,UAAU,uBACZ,yBACA;QAEJ,MAAM,UAAU,uBACZ,8FACA;QAEJ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAA2C;;;;;;kCACxD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;kCAET,uBAAuB,mBAAmB;;;;;;;;;;;;;;;;;IAKrD;IAEA,IAAI,uBAAuB;QACzB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgD;;;;;;kCAC9D,6LAAC;wBAAE,WAAU;kCACV,WACG,wFACA;;;;;;kCAEN,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAAwC;;;;;;8BACrD,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;;IAGxB;IAEA,MAAM,iBAAiB;QACrB,IAAI,QAAQ,OAAO,EAAE;YACnB,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,QAAQ,OAAO,CAAC,qBAAqB;gBACvD,MAAM,eAAe,QAAQ,OAAO,CAAC,YAAY;gBAEjD,MAAM,UAAU,MAAM,CAAA,GAAA,qJAAA,CAAA,SAAkB,AAAD,EAAE,QAAQ,OAAO,EAAE;oBACxD,SAAS;oBACT,YAAY;oBACZ,iBAAiB;oBACjB,aAAa;oBACb,cAAc;oBACd,OAAO;wBACL,QAAQ;wBACR,SAAS;oBACX;gBACF;gBAEA,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,KAAK,QAAQ,GAAG,WAAW,gCAAgC;gBAC3D,KAAK,KAAK;YACZ,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,IAAI,iBAAiB;QACnB,MAAM,WAAW;QACjB,MAAM,YAAY,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE,UAAU,MAAM,CAAC,kBAAkB,EAAE,YAAY,UAAU,EAAE,CAAC,EAAE,WAAW,UAAU,OAAO,WAAW,EAAE,WAAW,WAAW,QAAQ,2BAA2B,CAAC;QAEnN,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,MAAM,oBAAoB,cAAc,KAAK,KAAK,CAAC,eAAe;QAClE,MAAM,cAAc,oBAAoB,GAAG,kBAAkB,SAAS,CAAC,CAAC,EAAE,kBAAkB,QAAQ,EAAE,GAAG;QAEzG,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,KAAK;wBAAS,WAAU;;0CAC3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAE;;;;;;kDACH,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;;;;;;;0CAGZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,6LAAC;gCAAG,WAAU;;oCAAmF,WAAW,WAAW;oCAAQ;;;;;;;0CAC/H,6LAAC;gCAAE,WAAU;;oCAAwE;kDAClE,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;;0CAElE,6LAAC;gCAAE,WAAU;0CAA8D;;;;;;0CAG3E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,UAAU,MAAM,GAAG,IAAI,AAAC,iBAAiB,UAAU,MAAM,GAAI,MAAM,EAAE,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAG/F,6LAAC;gCAAE,WAAU;;oCAA8D;kDAC5D,6LAAC;wCAAK,WAAU;kDAAa;;;;;;oCAAsB;oCAAI,UAAU,MAAM;;;;;;;0CAEtF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,6LAAC;4CAAK,WAAU;sDACb,gBAAgB,sBAAsB,cAAc,yBAAyB,CAAC,WAAW,EAAE,YAAY,UAAU,EAAE,CAAC,EAAE,WAAW,UAAU,QAAQ;;;;;;;;;;;;;;;;;0CAI1J,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAsB;4CAAE,iBAAiB,CAAC,YAAY,UAAU,CAAC;;;;;;;kDAC5E,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAe;4CAAG,YAAY,UAAU;4CAAE;;;;;;;;;;;;;;;;;;;kCAIzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAkD;;;;;;kDAG/D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,kJAAA,CAAA,sBAAmB;4CAAC,KAAK;4CAAU,OAAO;sDACzC,cAAA,6LAAC,kJAAA,CAAA,eAAY;gDAAC,MAAM;gDAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;0CAInC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQb;IAEA,MAAM,kBAAkB,SAAS,CAAC,qBAAqB;IAEvD,qBACE,6LAAC;QAAI,WAAU;;YACZ,8BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAqC;gCAAO,WAAW,WAAW;gCAAQ;;;;;;;sCACxF,6LAAC;4BAAE,WAAU;sCAAwD;;;;;;sCAGrE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;8CAEN,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAGR,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;;gCACX;gCACQ,WAAW,WAAW;gCAAQ;;;;;;;;;;;;;;;;;;YAM5C,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCACrE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;sCAGvD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,eAAe;4BAC9B,WAAU;sCACX;;;;;;;;;;;;;;;;;YAON,iCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAChE,6LAAC;4BAAE,WAAU;sCACV,sBAAsB;;;;;;sCAEzB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;YAON,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,uCAC/D;;kCACE,6LAAC;;;;;kCAED,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,SAAS,CAAC,CAAC;4BAAC;;;;;;;;;;;kCAGnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDACb,WAAW;;;;;;;;;;;;8CAGhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDAA8G;oDAClH,uBAAuB;oDAAE;oDAAK,UAAU,MAAM;;;;;;;;;;;;sDAG5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,gBAAgB,QAAQ;;;;;;8DAE3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,WAAW,eAAe;4DAC1B,SAAS,IAAM,kBAAkB;4DACjC,UAAU,mBAAmB;;8EAE7B,6LAAC;oEAAK,WAAU;8EAA4H;;;;;;8EAG5I,6LAAC;oEAAK,WAAU;8EAAkD,gBAAgB,SAAS;;;;;;;;;;;;sEAE7F,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,WAAW,eAAe;4DAC1B,SAAS,IAAM,kBAAkB;4DACjC,UAAU,mBAAmB;;8EAE7B,6LAAC;oEAAK,WAAU;8EAA4H;;;;;;8EAG5I,6LAAC;oEAAK,WAAU;8EAAkD,gBAAgB,SAAS;;;;;;;;;;;;sEAE7F,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,WAAW,eAAe;4DAC1B,SAAS,IAAM,kBAAkB;4DACjC,UAAU,mBAAmB;;8EAE7B,6LAAC;oEAAK,WAAU;8EAA4H;;;;;;8EAG5I,6LAAC;oEAAK,WAAU;8EAAkD,gBAAgB,WAAW;;;;;;;;;;;;sEAE/F,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,WAAW,eAAe;4DAC1B,SAAS,IAAM,kBAAkB;4DACjC,UAAU,mBAAmB;;8EAE7B,6LAAC;oEAAK,WAAU;8EAA4H;;;;;;8EAG5I,6LAAC;oEAAK,WAAU;8EAAkD,gBAAgB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAIlG,6LAAC,qIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,SAAS,IAAM;4CACf,UAAU,mBAAmB;sDAE5B,yBAAyB,UAAU,MAAM,GAAG,IAAI,WAAW;;;;;;;;;;;;8CAGhE,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GA1hCwB;;QACP,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QAClB,gIAAA,CAAA,UAAY;;;MAHR", "debugId": null}}]}