import { SetStateAction } from "react";

// Exam-related Types
export interface Exam {
  total_student_intake: number | undefined;
  id: number;
  exam_name: string;
  start_date: string;
  duration: number;
  marks: string | number;
  total_questions: number;
  level: "easy" | "medium" | "hard";
  // max_classes_can_join: number;
  // max_questions_class_can_apply?: number;
  start_registration_date?: string;
  coins_required: number;
  exam_type: "CLASSES" | "STUDENTS";

  createdAt: string;
  updatedAt: string;
}

export interface TransformedExam {
  id: number;
  exam_name: string;
  start_date: string;
  duration: number;
  marks: number;
  total_student_intake: number;
  total_questions: number;
  level: "easy" | "medium" | "hard";
  coins_required: number;
  exam_type: "CLASSES" | "STUDENTS";
  start_registration_date: string;
  createdAt: string;
  updatedAt: string;
}

export interface ExamInput {
  exam_name: string;
  start_date: string;
  duration: number;
  marks: number;
  total_student_intake: number;
  total_questions: number;
  level: "easy" | "medium" | "hard";
  // max_classes_can_join: number;
  // max_questions_class_can_apply: number;
  start_registration_date?: string;
  coins_required?: number;
  exam_type?: "CLASSES" | "STUDENTS";
}

// Applicant Classes Type
export interface ExamClass {
  id: number;
  examName: string;
  classesName: string;
  status: "ACCEPT" | "REJECT" | "PENDING";
}

export interface PaginatedResponse<T> {
  data: SetStateAction<ExamClass[]>;
  max_questions_class_can_apply: SetStateAction<number>;
  exam_name: string;
  exam: any;
  questions: T[];
  total_questions: number;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Question-related Types
export interface Question {
  id: number;
  examId: number;
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAns: string;
  createdAt: string;
  updatedAt: string;
}

export interface QuestionInput {
  examId: number;
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAns: string;
}

// For Results
export interface Result {
  userId: number;
  userName: string;
  examName: string;
  correctAnswers: number;
  totalQuestions: number;
  attempted: boolean;
  success: boolean;
  message: string;
  results: Result[];
}

export interface Answer {
  id: number;
  userId: number;
  questionId: number;
  answer: string;
}

export interface ExamQuestionsResponse {
  questions: Question[];
  total_questions: number;
  exam_name: string;
}

//types for quiz
export interface Option {
  key: string;
  value: string;
}

export interface Question {
  id: number;
  text: string;
  options: Option[];
  correctAnswer: string;
}

export interface QuizCardProps {
  examId: number | null;
}

export interface ExamApplication {
  id: string;
  examId: number;
  classId: string;
  createdAt: string;
  class: {
    firstName: string;
    lastName: string;
    className: string | null;
  };
  exam: { exam_name: string };
  classes: { firstName: string; lastName: string; className: string | null };
}

export interface PaginationResponse {
  applications: ExamApplication[];
  total: number;
  currentPage: number;
  totalPages: number;
}

// for email
export interface emailData {
  email?: any;
  subject: string;
  message: any;
  status?: string;
}

//question bank
export interface QuestionBank {
  id: string;
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAnswer: "optionOne" | "optionTwo" | "optionThree" | "optionFour";
  medium: "ENGLISH" | "GUJARATI";
  standard: string;
  subject: string;
  level: "EASY" | "MEDIUM" | "HARD";
  chapter:  'Polynomial'| 'Statics' | 'Probability';
}

export interface QuestionBankInput {
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAnswer:
    | "optionOne"
    | "optionTwo"
    | "optionThree"
    | "optionFour"
    | undefined;
  medium: "ENGLISH" | "GUJARATI" | undefined;
  standard: string;
  subject: string;
  level: "EASY" | "MEDIUM" | "HARD" | undefined;
    chapter:  'Polynomial'| 'Statics' | 'Probability';
}
export interface Blog {
  id: string;
  blogTitle: string;
  blogImage: string;
  blogDescription: string;
  status: "PENDING" | "APPROVED" | "REJECTED";
  createdAt: string;
  updatedAt: string;
  classId?: string;
  class?: {
    id: string;
    firstName: string;
    lastName: string;
    className: string;
  };
}

export interface PaginatedBlogResponse {
  blogs: Blog[];
  total: number;
  currentPage: number;
  totalPages: number;
}

export interface Thought {
  id: string;
  classId: string;
  thoughts: string;
  status: "PENDING" | "APPROVED" | "REJECTED";
  createdAt: string;
  updatedAt: string;
  class: {
    id: string;
    firstName: string;
    lastName: string;
    className: string;
    contactNo: string;
    email: string;
    username: string;
  };
}

export interface Reviews {
  id: string;
  message: string;
  rating: number;
  createdAt: string;
  classId: string;
  studentName: string;
  class: {
    className: string;
  };
}

export interface Testimonial {
  id: string;
  message: string;
  rating: number;
  status: "PENDING" | "APPROVED" | "REJECTED";
  createdAt: string;
  class: {
    className: string;
    firstName: string;
    lastName: string;
    fullName?: string;
    classesLogo?: string | null;
    profilePhoto?: string | null;
    tutorBio?: string | null;
    ClassAbout?: {
      classesLogo?: string | null;
      profilePhoto?: string | null;
      tutorBio?: string | null;
    };
  };
}

export interface Student {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  contact: string;
  coins?: number;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
  profile?: StudentProfile;
}

export interface StudentProfile {
  id: string;
  studentId: string;
  medium: string;
  classroom: string;
  birthday: string;
  school: string;
  address: string;
  photo?: string;
  documentUrl?: string;
  status: "PENDING" | "APPROVED" | "REJECTED";
  createdAt: string;
  updatedAt: string;
  student?: Student;
}

export interface UwhizPriceRank {
  id: string;
  examId: number;
  rank: number;
  price: number;
  exam?: {
    exam_name: string;
  };
}

export interface Applicant {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  contact:string
}

export interface ApplicantsResponse {
  total: number;
  page: number;
  limit: number;
  data: Applicant[];
}

export type Applicants = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  contact:string
};

export type ApiResponse = {
  success: boolean;
  total: number;
  page: number;
  limit: number;
  data: Applicant[] | terminatedStudents[];
};

export type ClassData = {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  contactNo: string;
  className: string;
  isVerified: boolean;
  createdAt: string;
  ClassAbout: {
    id: string;
    birthDate: string;
    catchyHeadline: string;
    tutorBio: string;
    profilePhoto: string;
    classesLogo: string;
  };
  education: {
    id: string;
    degree: string;
    university: string;
    passoutYear: string;
    degreeType: string;
    isDegree: boolean;
  }[];
  experience: {
    id: string;
    title: string;
    from: string;
    to: string;
    isExperience: boolean;
  }[];
  certificates: {
    id: string;
    title: string;
    isCertificate: boolean;
    certificateUrl: string;
  }[];
  tuitionClasses: {
    id: string;
    education: string;
    coachingType: string;
    boardType: string;
    medium: string;
    section: string;
    subject: string;
    details: string;
  }[];
  status:{
    id:string;
    status:string;
  }
};

export interface subjectPrefrence {
  examId: number;
  weightage: number;
  subject?: string;
  level?: string;
  id?: string;
}

export interface LevelPreference {
  id?: string;
  level: string;
  weightage: number;
  examId:number;
}


export type terminatedStudents = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  reason:string;
  contact:string;
};

// Constants Management Types
export interface ConstantCategory {
  id: string;
  name: string;
  details: ConstantDetail[];
}

export interface ConstantDetail {
  id: string;
  name: string;
  categoryId: string;
  category: {
    id: string;
    name: string;
  };
  subDetails: ConstantSubDetail[];
}

export interface ConstantSubDetail {
  id: string;
  name: string;
  detailId: string;
  detail: {
    id: string;
    name: string;
    category: {
      id: string;
      name: string;
    };
  };
  values: ConstantSubDetailValue[];
}

export interface ConstantSubDetailValue {
  id: string;
  name: string;
  isActive: boolean;
  subDetailId: string;
  subDetail: {
    id: string;
    name: string;
    detail: {
      id: string;
      name: string;
      category: {
        id: string;
        name: string;
      };
    };
  };
}

// Constants API Request Types
export interface CreateCategoryRequest {
  name: string;
}

export interface UpdateCategoryRequest {
  name: string;
}

export interface CreateDetailRequest {
  name: string;
  categoryId: string;
}

export interface UpdateDetailRequest {
  name: string;
}

export interface CreateSubDetailRequest {
  name: string;
  detailId: string;
}

export interface UpdateSubDetailRequest {
  name: string;
}

export interface CreateValueRequest {
  name: string;
  subDetailId: string;
  isActive?: boolean;
}

export interface UpdateValueRequest {
  name: string;
  isActive?: boolean;
}



export interface MockQuestionBank {
  id: string;
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAnswer: "optionOne" | "optionTwo" | "optionThree" | "optionFour";
  quetionDate: Date | string;
  medium: "ENGLISH" | "GUJARATI";
  isWeeklyExam: boolean;
}

export interface MockQuestionBankInput {
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAnswer:
    | "optionOne"
    | "optionTwo"
    | "optionThree"
    | "optionFour"
    | undefined;
  medium: 'ENGLISH' | 'GUJARATI';
  isWeeklyExam: boolean;
}

export interface ChatMessage {
  id: string;
  text: string;
  sender: string;
  recipient?: string;
  timestamp: string;
  senderType?: string;
  recipientType?: string;
}

export interface OnlineUser {
  username: string;
  userType: string;
}

export interface AdminChatProps {
  isAuthenticated: boolean;
  loginPath: string;
}

export interface Class {
  id: string;
  firstName: string;
  lastName: string;
  className?: string;
  email: string;
  username: string;
  createdAt: string;
  latestMessageTime?: string;
}

export interface Student {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  latestMessageTime?: string;
}

export interface ChatMessage {
  id: string;
  text: string;
  sender: string;
  recipient?: string;
  timestamp: string;
  senderType?: string;
  recipientType?: string;
}

export interface ConversationResponse {
  classDetails: {
    id: string;
    name: string;
    username: string;
  };
  studentDetails: {
    id: string;
    name: string;
  };
  messages: ChatMessage[];
}

export type ClassesStudent = {
  student_full_name: string;
  date_of_birth?: string;
  email?: string;
  contact_no?: string;
  student_class_uuid?: string;
  year_name?:string
};

export interface ActivityLog {
  id: string;
  userId: string;
  userType: 'STUDENT' | 'CLASS';
  activityType: 'LOGIN' | 'LOGOUT' | 'REGISTRATION';
  createdAt: string;
  userDetails?: {
    id: string;
    firstName: string;
    lastName: string;
    email?: string;
    contact?: string;
    contactNo?: string;
    className?: string;
    profilePhoto?: string;
  };
}

export interface ActivityLogResponse {
  logs: ActivityLog[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ActivityLogStats {
  overview: {
    totalLogs: number;
    loginCount: number;
    logoutCount: number;
    registrationCount: number;
    studentLogs: number;
    classLogs: number;
  };
  dailyActivity: Array<{
    activityType: string;
    _count: number;
  }>;
}

export interface GetActivityLogsParams {
  page?: number;
  limit?: number;
  userType?: string;
  activityType?: string;
  searchName?: string;
  searchEmail?: string;
  searchContact?: string;
  searchActivity?: string;
}

export interface StoreItem {
  id: string;
  name: string;
  description: string;
  coinPrice: number;
  totalStock: number;
  availableStock: number;
  category: string;
  image: string | null;
  status: 'ACTIVE' | 'INACTIVE';
  createdAt: string;
  updatedAt: string;
}

export interface CreateStoreItemData {
  name: string;
  description: string;
  coinPrice: number;
  totalStock: number;
  category: string;
  image?: string;
}

export interface UpdateStoreItemData {
  name?: string;
  description?: string;
  coinPrice?: number;
  totalStock?: number;
  availableStock?: number;
  category?: string;
  image?: string;
  status?: 'ACTIVE' | 'INACTIVE';
}

export interface StoreFilters {
  category?: string;
  status?: string;
  search?: string;
}

export interface StoreStats {
  totalItems: number;
  activeItems: number;
  inactiveItems: number;
  outOfStockItems: number;
  categoriesCount: number;
  categories: Array<{
    category: string;
    count: number;
  }>;
}

export interface StoreOrder {
  id: string;
  modelId: string;
  modelType: 'STUDENT' | 'CLASS' | 'SCHOOL';
  buyerName?: string;
  buyerEmail?: string | null;
  buyerAddress?: string | null;
  itemId: string;
  itemName: string;
  itemPrice: number;
  quantity: number;
  totalCoins: number;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  waybill?: string | null;
  shippingStatus?: string | null;
  shippingError?: string | null;
  createdAt: string;
  updatedAt: string;
  item: {
    id: string;
    name: string;
    description: string;
    coinPrice: number;
    totalStock: number;
    availableStock: number;
    category: string;
    image: string | null;
    status: string;
  };
}

export interface StoreOrderStats {
  totalOrders: number;
  completedOrders: number;
  pendingOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
  todayOrders: number;
  thisMonthOrders: number;
}

export interface StoreOrderFilters {
  status?: string;
  search?: string;
  modelType?: string;
}

export interface StoreOrderPaginationResponse {
  orders: StoreOrder[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}
