import express from 'express';
import {
  createBankPayment,
  getBankPaymentDetails,
  updateBankPayment
} from '../controllers/classesBankPaymentDetailController';
import { authClientMiddleware } from '@/middlewares/clientAuth';

const bankPaymentRouter = express.Router();

bankPaymentRouter.post('/create', authClientMiddleware, createBankPayment);
bankPaymentRouter.put('/update/:id', authClientMiddleware, updateBankPayment);
bankPaymentRouter.get('/details', authClientMiddleware, getBankPaymentDetails);
bankPaymentRouter.get('/admin/details', getBankPaymentDetails);

export default bankPaymentRouter;