"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { ArrowLeft, Coins, Package, CreditCard, Plus, Minus, ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import Header from '@/app-components/Header';
import Footer from '@/app-components/Footer';
import { isAuthenticated } from '@/lib/utils';
import * as storeApi from '@/services/storeApi';
import * as cartApi from '@/services/cartApi';
import * as storePurchaseApi from '@/services/storePurchaseApi';
import { StoreItem } from '@/lib/types';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface StoreDetailPageProps {
  params: Promise<{ id: string }>;
}

const StoreDetailPage = ({ params }: StoreDetailPageProps) => {
  const router = useRouter();
  const [product, setProduct] = useState<StoreItem | null>(null);
  const [latestItems, setLatestItems] = useState<StoreItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [productId, setProductId] = useState<string>('');
  const [quantity, setQuantity] = useState(1);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [isUserLoggedIn, setIsUserLoggedIn] = useState(false);
  const [showDirectBuyDialog, setShowDirectBuyDialog] = useState(false);
  const [isDirectBuying, setIsDirectBuying] = useState(false);

  useEffect(() => {
    const getParamsId = async () => {
      const { id } = await params;
      setProductId(id);
    };

    getParamsId();
  }, [params]);

  useEffect(() => {
    const authStatus = isAuthenticated();
    setIsUserLoggedIn(authStatus.isAuth);
  }, []);

  useEffect(() => {
    const fetchProductAndLatestItems = async () => {
      try {
        if (!productId) return;

        setIsLoading(true);

        const productResult = await storeApi.getStoreItemById(productId);

        if (!productResult.success) {
          toast.error(productResult.error || 'Failed to load product');
          router.push('/store');
          return;
        }

        setProduct(productResult.data);

        const latestItemsResult = await storeApi.getAllStoreItems();

        if (latestItemsResult.success && latestItemsResult.data) {
          const latest4Items = latestItemsResult.data
            .filter((item: StoreItem) => item.id !== productId)
            .slice(0, 4);
          setLatestItems(latest4Items);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load product');
        router.push('/store');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProductAndLatestItems();
  }, [productId, router]);

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity < 1) return;
    if (product && newQuantity > product.availableStock) {
      toast.error(`Only ${product.availableStock} items available`);
      return;
    }
    setQuantity(newQuantity);
  };

  const addToCart = async () => {
    if (!isUserLoggedIn) {
      toast.error("Please login to add items to cart");
      return;
    }

    if (!product) return;

    if (product.availableStock === 0) {
      toast.error("Item is out of stock");
      return;
    }

    if (quantity > product.availableStock) {
      toast.error(`Only ${product.availableStock} items available`);
      return;
    }

    try {
      setIsAddingToCart(true);
      const result = await cartApi.addToCart(product.id, quantity);

      if (result.success) {
        toast.success(`${quantity} item(s) added to cart!`);
        window.dispatchEvent(new CustomEvent('cartUpdated'));
        setQuantity(1); 
      } else {
        toast.error(`Only ${product.availableStock} items available`);
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error("Item is out of stock");
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleDirectBuy = () => {
    if (!isUserLoggedIn) {
      toast.error("Please login to purchase items");
      return;
    }

    if (!product) return;

    if (product.availableStock === 0) {
      toast.error("Item is out of stock");
      return;
    }

    if (quantity > product.availableStock) {
      toast.error(`Only ${product.availableStock} items available`);
      return;
    }

    setShowDirectBuyDialog(true);
  };

  const confirmDirectPurchase = async () => {
    if (!product) return;

    try {
      setIsDirectBuying(true);
      const totalCoins = product.coinPrice * quantity;

      const purchaseData: storePurchaseApi.PurchaseData = {
        cartItems: [{
          id: product.id,
          name: product.name,
          coinPrice: product.coinPrice,
          quantity: quantity,
          image: product.image || ''
        }],
        totalCoins
      };

      const result = await storePurchaseApi.purchaseItems(purchaseData);

      if (!result.success) {
        if (result.error === 'PROFILE_NOT_APPROVED') {
          const errorMessage = result.data?.message || 'Your profile is not approved yet. Please complete your profile and wait for admin approval.';
          toast.error(errorMessage);
          setShowDirectBuyDialog(false);
          return;
        }
        throw new Error(result.error);
      }

      const orderId = result.data?.orderId || result.data?.firstOrderId || 'Unknown';
      toast.success(`Order placed successfully! Order ID: ${orderId.slice(-8)}. Coins deducted. Your order is pending admin approval.`);

      setShowDirectBuyDialog(false);
      setQuantity(1);

      const productResult = await storeApi.getStoreItemById(productId);
      if (productResult.success) {
        setProduct(productResult.data);
      }

      const latestItemsResult = await storeApi.getAllStoreItems();
      if (latestItemsResult.success && latestItemsResult.data) {
        const latest4Items = latestItemsResult.data
          .filter((item: StoreItem) => item.id !== productId)
          .slice(0, 4);
        setLatestItems(latest4Items);
      }
    } catch (error: any) {
      console.error('Error details:', error);
      toast.error(error.message || 'Purchase failed');
      setShowDirectBuyDialog(false);
    } finally {
      setIsDirectBuying(false);
    }
  };

  if (isLoading) {
    return (
      <>
        <Header />
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <div className="container mx-auto px-4 py-8">
            <Skeleton className="h-10 w-32 mb-6" />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Skeleton className="h-96 w-full rounded-lg" />
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-6 w-1/4" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  if (!product) {
    return (
      <>
        <Header />
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-foreground mb-4">Product Not Found</h1>
            <Button onClick={() => router.push('/store')} className="bg-customOrange hover:bg-orange-600">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Store
            </Button>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Header />
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          {/* Back Button */}
          <Button
            variant="ghost"
            onClick={() => router.push('/store')}
            className="mb-6 hover:bg-muted"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Store
          </Button>

          {/* Product Details */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            {/* Product Image */}
            <Card className="overflow-hidden">
              <CardContent className="p-0">
                <div className="relative h-96 lg:h-[500px] bg-muted/30 flex items-center justify-center">
                  <Image
                    src={
                      product.image?.startsWith('http')
                        ? product.image
                        : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${product.image?.startsWith('/') ? product.image.substring(1) : product.image || 'uploads/store/placeholder.jpg'}`
                    }
                    alt={product.name}
                    className="object-contain w-full h-full"
                    width={500}
                    height={500}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/logo.png";
                    }}
                  />
                  {product.availableStock === 0 && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                      <Badge variant="destructive" className="text-lg px-4 py-2">Out of Stock</Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Product Information */}
            <div className="space-y-6">
              <div>
                <div className="flex items-start justify-between mb-2">
                  <h1 className="text-3xl font-bold text-foreground">{product.name}</h1>
                  <Badge variant="secondary" className="text-sm">
                    {product.category}
                  </Badge>
                </div>
                <p className="text-muted-foreground text-lg leading-relaxed">
                  {product.description}
                </p>
              </div>

              {/* Price */}
              <div className="bg-card p-6 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <Coins className="w-6 h-6 text-customOrange" />
                  <span className="text-3xl font-bold text-customOrange">
                    {product.coinPrice} coins
                  </span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Pay with your UEST coins
                </p>
              </div>

              {/* Stock Information */}
              <div className="bg-card p-4 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <Package className="w-5 h-5 text-muted-foreground" />
                  <span className="font-medium text-card-foreground">Stock Information</span>
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Available:</span>
                    <span className={`font-medium ${product.availableStock === 0 ? 'text-red-500' : 'text-green-600'}`}>
                      {product.availableStock} units
                    </span>
                  </div>
                </div>
              </div>

              {/* Quantity Selector and Add to Cart */}
              {product.availableStock > 0 && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-card-foreground mb-2">
                      Quantity
                    </label>
                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuantityChange(quantity - 1)}
                        disabled={quantity <= 1}
                      >
                        <Minus className="w-4 h-4" />
                      </Button>
                      <span className="w-12 text-center font-medium text-lg">{quantity}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuantityChange(quantity + 1)}
                        disabled={quantity >= product.availableStock}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Total Cost:</span>
                      <span className="font-bold text-lg text-customOrange flex items-center">
                        <Coins className="w-5 h-5 mr-1" />
                        {product.coinPrice * quantity} coins
                      </span>
                    </div>

                    <div className="grid grid-cols-1 gap-3">
                      <Button
                        onClick={addToCart}
                        disabled={isAddingToCart || !isUserLoggedIn}
                        variant="outline"
                        className="w-full border-customOrange text-customOrange hover:bg-customOrange hover:text-white py-3 text-lg"
                      >
                        {isAddingToCart ? (
                          <>
                            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                            Adding to Cart...
                          </>
                        ) : (
                          <>
                            <ShoppingCart className="w-5 h-5 mr-2" />
                            Add to Cart
                          </>
                        )}
                      </Button>

                      <Button
                        onClick={handleDirectBuy}
                        disabled={!isUserLoggedIn}
                        className="w-full bg-customOrange hover:bg-orange-600 text-white py-3 text-lg"
                      >
                        <CreditCard className="w-5 h-5 mr-2" />
                        Buy Now
                      </Button>
                    </div>



                    {!isUserLoggedIn && (
                      <p className="text-sm text-muted-foreground text-center">
                        Please login to purchase items
                      </p>
                    )}
                  </div>
                </div>
              )}

              {product.availableStock === 0 && (
                <div className="bg-destructive/10 border border-destructive/20 p-4 rounded-lg">
                  <p className="text-destructive font-medium text-center">
                    This item is currently out of stock
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Additional Information */}
          <Card className='mb-8'>
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-card-foreground mb-4">Product Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium text-card-foreground mb-2">Category</h3>
                  <p className="text-muted-foreground">{product.category}</p>
                </div>
                <div>
                  <h3 className="font-medium text-card-foreground mb-2">Status</h3>
                  <Badge variant={product.status === 'ACTIVE' ? 'default' : 'secondary'}>
                    {product.status}
                  </Badge>
                </div>
                <div>
                  <h3 className="font-medium text-card-foreground mb-2">Added On</h3>
                  <p className="text-muted-foreground">
                    {new Date(product.createdAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
                <div>
                  <h3 className="font-medium text-card-foreground mb-2">Last Updated</h3>
                  <p className="text-muted-foreground">
                    {new Date(product.updatedAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Latest Items Section */}
          {latestItems.length > 0 && (
            <Card className="mb-8">
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold text-card-foreground mb-6">Latest Items</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                  {latestItems.map((item) => (
                    <Card
                      key={item.id}
                      className="overflow-hidden group bg-card border shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer"
                      onClick={() => router.push(`/store/${item.id}`)}
                    >
                      <div className="relative h-48 bg-muted/30 flex items-center justify-center">
                        <Image
                          src={
                            item.image?.startsWith('http')
                              ? item.image
                              : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${item.image?.startsWith('/') ? item.image.substring(1) : item.image || 'uploads/store/placeholder.jpg'}`
                          }
                          alt={item.name}
                          className="object-contain w-full h-full"
                          width={200}
                          height={200}
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = "/logo.png";
                          }}
                        />
                        {item.availableStock === 0 && (
                          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                            <Badge variant="destructive" className="text-sm">Out of Stock</Badge>
                          </div>
                        )}
                      </div>
                      <CardContent className="p-4">
                        <div className="space-y-2">
                          <div className="flex items-start justify-between">
                            <h3 className="font-semibold text-card-foreground text-sm line-clamp-2 group-hover:text-customOrange transition-colors">
                              {item.name}
                            </h3>
                            <Badge variant="secondary" className="text-xs ml-2 shrink-0">
                              {item.category}
                            </Badge>
                          </div>
                          <p className="text-muted-foreground text-xs line-clamp-2">
                            {item.description}
                          </p>
                          <div className="flex items-center justify-between pt-2">
                            <div className="flex items-center gap-1">
                              <Coins className="w-4 h-4 text-customOrange" />
                              <span className="font-bold text-customOrange text-sm">
                                {item.coinPrice}
                              </span>
                            </div>
                            <span className={`text-xs ${item.availableStock === 0 ? 'text-red-500' : 'text-green-600'}`}>
                              {item.availableStock} left
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
      <Footer />

      {/* Direct Buy Confirmation Dialog */}
      <AlertDialog open={showDirectBuyDialog} onOpenChange={setShowDirectBuyDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Direct Purchase</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to purchase this item directly for <strong>{product?.coinPrice * quantity} coins</strong>?
              <br />
              <br />
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{product?.name} x{quantity}</span>
                  <span>{product?.coinPrice * quantity} coins</span>
                </div>
              </div>
              <br />
              This action cannot be undone and coins will be deducted from your account immediately.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDirectPurchase}
              disabled={isDirectBuying}
              className="bg-customOrange hover:bg-customOrange/90"
            >
              {isDirectBuying ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Processing...
                </>
              ) : (
                'Confirm Purchase'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default StoreDetailPage;
