import { z } from 'zod';

const commonFields = {
  bankName: z.string().min(1, 'Bank name is required'),
  accountNumber: z
    .string()
    .min(9, 'Account number too short')
    .max(18, 'Account number too long'),
  reAccountNumber: z.string().min(9),
  ifscCode: z
    .string()
    .regex(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Invalid IFSC code'),
  accountHolderName: z.string().min(1, 'Account holder name is required'),
  branchName: z.string().min(1, 'Branch name is required'),
  upiId: z.string().optional(),
};

const bankSchema = z.object({
  defaultMethod: z.literal('BANK'),
  ...commonFields,
});

const upiSchema = z.object({
  defaultMethod: z.literal('UPI'),
  upiId: z
    .string()
    .regex(/^[\w.-]{2,}@[\w]{2,}$/, 'Invalid UPI ID'),
});

const baseSchema = z.discriminatedUnion('defaultMethod', [
  bankSchema,
  upiSchema,
]);

export const createBankPaymentSchema = baseSchema.superRefine((data, ctx) => {
  if (data.defaultMethod === 'BANK' && data.accountNumber !== data.reAccountNumber) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Account numbers do not match',
      path: ['reAccountNumber'],
    });
  }
});

export const updateBankPaymentSchema = createBankPaymentSchema;