"use client";

import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useEffect, useState, useRef } from "react";
import { getMockExamResults } from "@/services/mock-exam-resultApi";

export default function WeeklyExamButton() {
  const router = useRouter();
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [remainingSeconds, setRemainingSeconds] = useState<number | null>(null);
  const [studentId, setStudentId] = useState<string | null>(null);
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const reEnableTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    try {
      const data = localStorage.getItem("student_data");
      const fetchedStudentId = data ? JSON.parse(data).id : null;
      setStudentId(fetchedStudentId);
    } catch (error) {
      console.error("Error retrieving studentId:", error);
      setStudentId(null);
    }
  }, []);

  useEffect(() => {
    const checkExamAttempt = async () => {
      const now = new Date();
      const isSunday = now.getDay() === 0; 

      if (!studentId) {
        setIsButtonDisabled(true);
        setRemainingSeconds(null);
        return;
      }

      try {
        // Calculate the start of the current week (Monday)
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - (now.getDay() === 0 ? 6 : now.getDay() - 1));
        startOfWeek.setHours(0, 0, 0, 0);

        // Calculate time until next Sunday
        const nextSunday = new Date(now);
        nextSunday.setDate(now.getDate() + (now.getDay() === 0 ? 7 : 7 - now.getDay()));
        nextSunday.setHours(0, 0, 0, 0);
        const remainingMs = nextSunday.getTime() - now.getTime();
        const remainingSec = Math.ceil(remainingMs / 1000);

        // Check if a WEEKLY exam was attempted this week
        const response = await getMockExamResults(studentId, 1, 1, { isWeekly: true });
        if (response.success && response.data.data.mockExamResults.length > 0) {
          const latestExam = response.data.data.mockExamResults[0];
          const examDate = new Date(latestExam.createdAt);

          // Check if the weekly exam was taken within the current week
          const hasAttemptedThisWeek = examDate >= startOfWeek;

          if (hasAttemptedThisWeek || !isSunday) {
            setIsButtonDisabled(true);
            setRemainingSeconds(remainingSec > 0 ? remainingSec : null);

            // Start countdown timer
            countdownIntervalRef.current = setInterval(() => {
              setRemainingSeconds(prev => {
                if (prev === null || prev <= 1) {
                  clearInterval(countdownIntervalRef.current!);
                  return null;
                }
                return prev - 1;
              });
            }, 1000);

            // Re-enable button on next Sunday
            reEnableTimeoutRef.current = setTimeout(() => {
              setIsButtonDisabled(!isSunday);
              setRemainingSeconds(null);
            }, remainingMs);
          } else {
            setIsButtonDisabled(false);
            setRemainingSeconds(null);
          }
        } else {
          // No weekly exam results; enable button only if it's Sunday
          setIsButtonDisabled(!isSunday);
          if (!isSunday) {
            setRemainingSeconds(remainingSec > 0 ? remainingSec : null);

            countdownIntervalRef.current = setInterval(() => {
              setRemainingSeconds(prev => {
                if (prev === null || prev <= 1) {
                  clearInterval(countdownIntervalRef.current!);
                  return null;
                }
                return prev - 1;
              });
            }, 1000);

            reEnableTimeoutRef.current = setTimeout(() => {
              setIsButtonDisabled(false);
              setRemainingSeconds(null);
            }, remainingMs);
          }
        }
      } catch (error) {
        console.error("Error checking exam attempt:", error);
        toast.error("Failed to verify exam eligibility.");
        setIsButtonDisabled(true);
        setRemainingSeconds(null);
      }
    };

    checkExamAttempt();

    return () => {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
      if (reEnableTimeoutRef.current) {
        clearTimeout(reEnableTimeoutRef.current);
      }
    };
  }, [studentId]);

  const handleMockExam = () => {
    if (!studentId) {
      toast.error("Please log in to attempt the exam.");
      router.push("/login");
      return;
    }

    if (isButtonDisabled && remainingSeconds) {
      toast.error(`You can attempt the exam again in ${formatRemainingTime()}.`);
      return;
    }

    if (new Date().getDay() !== 0) {
      toast.error("The weekly exam is only available on Sundays.");
      return;
    }

    router.push("/mock-test?isWeekly=true");
  };

  const formatRemainingTime = () => {
    if (remainingSeconds === null) return null;
    const days = Math.floor(remainingSeconds / (3600 * 24));
    const hours = Math.floor((remainingSeconds % (3600 * 24)) / 3600);
    const minutes = Math.floor((remainingSeconds % 3600) / 60);
    const seconds = remainingSeconds % 60;
    return days > 0
      ? `${days}d ${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
      : `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  return (
    <div className="flex flex-col">
      <Button
        className="w-full mx-auto bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
        onClick={handleMockExam}
        disabled={isButtonDisabled || !studentId}
      >
        Try Weekly Exam
      </Button>
      {isButtonDisabled && remainingSeconds && (
        <p className="text-center mt-2 text-gray-500 text-sm">
          You can attempt it after: {formatRemainingTime()}
        </p>
      )}
    </div>
  );
}