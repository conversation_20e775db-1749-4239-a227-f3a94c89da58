"use client";

import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { getMockExamResults } from "@/services/mock-exam-resultApi";
import { useParams } from "next/navigation";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { motion } from "framer-motion";
import TotalCoins from "@/components/ui/totaluestcoin";
import BadgeDisplay from "@/components/ui/badgedisplay";
import Image from "next/image";

interface MockExamResult {
  id: string;
  studentId: string;
  score: number;
  coinEarnings: number;
  createdAt: string;
  updatedAt: string;
}

interface Streak {
  streakCount: number;
  lastAttempt: string | null;
}

interface Badge {
  streakCount: number;
  badges: {
    badgeType: string;
    badgeSrc: string;
    badgeAlt: string;
    count?: number;
  }[];
  badgeType: string | null;
  badgeSrc: string | null;
  badgeAlt: string | null;
}

interface MockExamData {
  mockExamResults: MockExamResult[];
  streak: Streak;
  badge: Badge;
  pagination: {
    totalResults: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
}

function ViewMockExamResult() {
  const params = useParams();
  const studentId = params.studentId as string;
  const [data, setData] = useState<MockExamData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCoins, setTotalCoins] = useState<number | null>(null);
  const [coinBadgeSrc, setCoinBadgeSrc] = useState<string | null>(null);
  const [coinBadgeAlt, setCoinBadgeAlt] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const limit = 10;

  useEffect(() => {
    const fetchResults = async () => {
      if (!studentId) {
        setError("Student ID is required");
        setLoading(false);
        return;
      }
      setLoading(true);
      setError(null);
      try {
        const response = await getMockExamResults(studentId, currentPage, limit, { isWeekly: false });
        if (response.success && response.data?.data) {
          setData(response.data.data);

          const { mockExamResults } = response.data.data;
          const totalCoins = mockExamResults.reduce(
            (sum: number, result: any) => sum + (result.coinEarnings || 0),
            0
          );
          setTotalCoins(totalCoins);

          let badgeSrc = null;
          let badgeAlt = null;
          if (totalCoins >= 100 && totalCoins <= 499) {
            badgeSrc = "/scholer.svg";
            badgeAlt = "Scholar Badge";
          } else if (totalCoins >= 500 && totalCoins <= 999) {
            badgeSrc = "/Mastermind.svg";
            badgeAlt = "Mastermind Badge";
          } else if (totalCoins >= 1000) {
            badgeSrc = "/Achiever.svg";
            badgeAlt = "Achiever Badge";
          }
          setCoinBadgeSrc(badgeSrc);
          setCoinBadgeAlt(badgeAlt);
        } else {
          setError(response.error || "Failed to fetch daily quiz results");
        }
      } catch (err: any) {
        setError(err.message || "An unexpected error occurred");
      } finally {
        setLoading(false);
      }
    };

    fetchResults();
  }, [studentId, currentPage]);

  const handleNextPage = () => {
    if (data?.pagination && currentPage < data.pagination.totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date
      .toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      })
      .split("/")
      .join("-");
  };

  if (loading) {
    return (
      <div className="bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen flex items-center justify-center">
        <div className="text-center text-xl font-medium text-gray-700 animate-pulse">
          Loading daily quiz results...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen flex items-center justify-center">
        <div className="text-center text-xl font-medium text-red-600 bg-red-50 p-4 rounded-lg shadow-md">
          Error: {error}
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen flex items-center justify-center">
        <div className="text-center text-xl font-medium text-gray-700">
          No data available
        </div>
      </div>
    );
  }

  return (
    <>
      <Header />
      <div className="bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-4xl font-extrabold text-gray-800 text-center mb-10 tracking-tight">
            Daily Quiz Results
          </h2>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-10 rounded-3xl p-[2px] bg-gradient-to-tr from-orange-400 via-yellow-400 to-amber-500"
          >
            <div className="rounded-3xl bg-white/80 backdrop-blur-md p-6 md:p-8 shadow-xl">
              <div className="flex flex-col sm:flex-row items-start justify-between gap-6 relative">
                <motion.div
                  animate={{ scale: [1, 1.1, 1], rotate: [0, 1, -1, 0] }}
                  transition={{ repeat: Infinity, duration: 2, ease: "easeInOut" }}
                  className="w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-inner ring-2 ring-amber-200"
                >
                  <span className="text-3xl">🔥</span>
                </motion.div>

                <div className="text-center sm:text-left flex-1">
                  <h3 className="text-2xl font-semibold text-neutral-800 mb-1 tracking-tight">
                    Daily Streak
                  </h3>
                  <p className="text-4xl font-extrabold text-amber-600 tracking-wider">
                    {data.streak.streakCount} {data.streak.streakCount === 1 ? "Day" : "Days"}
                  </p>
                  <p className="text-sm text-gray-500 mt-1 italic">Stay consistent and keep growing</p>
                  <BadgeDisplay badge={data.badge} />
                </div>

                <div className="flex flex-col items-end gap-2">
                  <div className="px-4 py-2 bg-white rounded-full shadow ring-1 ring-amber-300 text-sm font-medium text-amber-600">
                    Consistency Reward
                  </div>
                  <TotalCoins
                    totalCoins={totalCoins}
                    badgeSrc={coinBadgeSrc}
                    badgeAlt={coinBadgeAlt}
                  />
                </div>
              </div>
            </div>
          </motion.div>

          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="space-y-4">
              {data.mockExamResults.length === 0 ? (
                <div className="text-center text-gray-500 text-lg font-medium py-8">
                  No daily quiz results found.
                </div>
              ) : (
                data.mockExamResults.map((result, index) => (
                  <div
                    key={result.id}
                    className={cn(
                      "flex items-center justify-between p-5 rounded-xl transition-all duration-200",
                      index === 0
                        ? "bg-orange-50 border-l-4 border-orange-500"
                        : "bg-gray-50 hover:bg-gray-100 hover:shadow-md"
                    )}
                  >
                    <div className="flex items-center gap-5">
                      <div
                        className={cn(
                          "w-12 h-12 flex items-center justify-center rounded-full font-semibold text-sm shadow-sm",
                          index === 0 ? "bg-orange-500 text-white" : "bg-gray-200 text-gray-700"
                        )}
                      >
                        #{(currentPage - 1) * limit + index + 1}
                      </div>
                      <div>
                        <p
                          className={cn(
                            "font-semibold",
                            index === 0 ? "text-lg text-gray-800" : "text-base text-gray-700"
                          )}
                        >
                          {formatDate(result.createdAt)}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-6">
                      {result.coinEarnings >= 0 && (
                        <div className="flex items-center gap-1.5 text-base font-semibold text-yellow-600 bg-yellow-100 px-4 py-1.5 rounded-full shadow-sm">
                          <Image
                            src="/uest_coin.png"
                            alt="Coin"
                            width={20}
                            height={20}
                            className="w-5 h-5"
                          />
                          {result.coinEarnings}
                        </div>
                      )}
                      <div
                        className={cn(
                          "font-semibold text-orange-600 bg-orange-100 px-4 py-1.5 rounded-full shadow-sm",
                          index === 0 ? "text-base" : "text-sm"
                        )}
                      >
                        {result.score}/10
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {data?.pagination?.totalPages > 1 && (
              <div className="flex justify-center gap-6 mt-8">
                <button
                  onClick={handlePreviousPage}
                  disabled={currentPage === 1}
                  className={cn(
                    "bg-orange-500 text-white font-semibold py-2.5 px-6 rounded-full transition-all duration-200",
                    currentPage === 1 ? "opacity-50 cursor-not-allowed" : "hover:bg-orange-600 hover:scale-105"
                  )}
                >
                  Previous
                </button>
                <span className="self-center text-gray-600 font-medium">
                  Page {currentPage} of {data?.pagination?.totalPages || 1}
                </span>
                <button
                  onClick={handleNextPage}
                  disabled={currentPage === (data?.pagination?.totalPages || 1)}
                  className={cn(
                    "bg-orange-500 text-white font-semibold py-2.5 px-6 rounded-full transition-all duration-200",
                    currentPage === (data?.pagination?.totalPages || 1) ? "opacity-50 cursor-not-allowed" : "hover:bg-orange-600 hover:scale-105"
                  )}
                >
                  Next
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default ViewMockExamResult;