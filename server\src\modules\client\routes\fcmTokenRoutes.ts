import express from 'express';
import {
  registerFcmTokenController,
  getUserFcmTokensController,
  deactivateFcmTokenController,
  removeFcmTokenController,
  registerStudentFcmTokenController,
  registerAdminFcmTokenController
} from '../controllers/fcmTokenController';
import { authClientMiddleware } from '@/middlewares/clientAuth';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';
import { authMiddleware } from '@/middlewares/adminAuth';

const router = express.Router();

// Routes for Classes (using authClientMiddleware)
router.post('/register', authClientMiddleware, registerFcmTokenController);
router.get('/my-tokens', authClientMiddleware, getUserFcmTokensController);
router.post('/deactivate', authClientMiddleware, deactivateFcmTokenController);
router.delete('/remove', authClientMiddleware, removeFcmTokenController);

// Routes for Students (using studentAuthMiddleware)
router.post('/student/register', studentAuthMiddleware, registerStudentFcmTokenController);

// Routes for Admin (using authMiddleware)
router.post('/admin/register', authMiddleware, registerAdminFcmTokenController);

export default router;
